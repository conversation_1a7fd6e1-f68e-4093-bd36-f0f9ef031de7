#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Hayvan <PERSON> - Sahte Veri Oluşturucu
Bu script veritabanına gerçekçi sahte veriler ekler.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database.database import SessionLocal, create_tables
from app.database.models import Farm, Feed, Animal
import random
from datetime import datetime, timedelta
from typing import List

def get_db_session():
    """Veritabanı session oluştur"""
    return SessionLocal()

def main():
    """Ana fonksiyon"""
    print("🚀 Sahte veri oluşturma başlıyor...")

    # Önce tabloları oluştur
    create_tables()

    db = get_db_session()

    try:
        # Çiftlik oluştur
        farm = Farm(
            name='Teslime Hanım Çiftliği',
            location='Bozdoğan, Aydın',
            established_date=datetime.strptime('2020-03-15', '%Y-%m-%d').date(),
            total_land_hectares=150.0,
            pasture_land_hectares=80.0,
            barn_capacity=200,
            feed_storage_capacity_tons=500.0,
            silage_capacity_tons=1000.0,
            hay_storage_capacity_tons=300.0,
            water_storage_capacity_liters=50000.0,
            milking_parlor_capacity=50,
            quarantine_facility_capacity=10,
            hospital_pen_capacity=5,
            handling_facility_present=True,
            scale_capacity_kg=1000.0
        )
        db.add(farm)
        db.flush()

        # Yemler oluştur
        feeds_data = [
            {'name': 'Süt İneği Konsantresi', 'feed_type': 'concentrate', 'cost_per_kg': 8.50, 'dry_matter_percentage': 88.0, 'crude_protein_percentage': 18.0, 'metabolizable_energy_mcal_kg': 2.8, 'storage_life_days': 180, 'moisture_content_percentage': 12.0},
            {'name': 'Mısır Silajı', 'feed_type': 'silage', 'cost_per_kg': 1.20, 'dry_matter_percentage': 35.0, 'crude_protein_percentage': 8.0, 'metabolizable_energy_mcal_kg': 2.4, 'storage_life_days': 365, 'moisture_content_percentage': 65.0},
            {'name': 'Yonca Kuru Otu', 'feed_type': 'hay', 'cost_per_kg': 2.80, 'dry_matter_percentage': 88.0, 'crude_protein_percentage': 18.0, 'metabolizable_energy_mcal_kg': 2.2, 'storage_life_days': 730, 'moisture_content_percentage': 12.0},
            {'name': 'Arpa', 'feed_type': 'grain', 'cost_per_kg': 4.20, 'dry_matter_percentage': 86.0, 'crude_protein_percentage': 11.5, 'metabolizable_energy_mcal_kg': 2.5, 'storage_life_days': 365, 'moisture_content_percentage': 14.0},
        ]

        for feed_data in feeds_data:
            feed = Feed(farm_id=farm.id, **feed_data)
            db.add(feed)

        # Hayvanlar oluştur
        breeds = ['holstein', 'angus', 'simmental']
        for i in range(20):
            breed = random.choice(breeds)
            gender = random.choice(['male', 'female'])
            age_months = random.randint(12, 60)
            birth_date = (datetime.now() - timedelta(days=age_months * 30.44)).date()

            # Irk özelliklerine göre ağırlık
            if breed == 'holstein':
                base_weight = 650 if gender == 'female' else 900
            elif breed == 'angus':
                base_weight = 550 if gender == 'female' else 850
            else:  # simmental
                base_weight = 750 if gender == 'female' else 1100

            # Yaşa göre ağırlık ayarlama
            age_factor = min(age_months / 24, 1.0)
            current_weight = base_weight * (0.3 + 0.7 * age_factor) + random.uniform(-50, 50)
            current_weight = max(current_weight, 50)

            # Durum belirleme
            if age_months < 12:
                status = 'calf'
            elif gender == 'female' and breed == 'holstein':
                status = random.choice(['breeding', 'milking', 'dry'])
            else:
                status = random.choice(['breeding', 'fattening'])

            animal = Animal(
                farm_id=farm.id,
                tag=f"{breed.upper()[:3]}-{i+1:03d}",
                breed=breed,
                birth_date=birth_date,
                gender=gender,
                current_weight_kg=round(current_weight, 1),
                body_condition_score=round(random.uniform(2.5, 4.5), 1),
                status=status,
                is_pregnant=False,
                purchase_price=random.uniform(8000, 25000) if random.random() < 0.7 else None,
                purchase_date=(datetime.now() - timedelta(days=random.randint(30, 1000))).date() if random.random() < 0.7 else None,
                notes=random.choice(['', 'Sağlıklı hayvan', 'Düzenli kontrol gerekli', 'Yüksek verimli'])
            )
            db.add(animal)

        db.commit()
        print("✅ Sahte veri oluşturma tamamlandı!")
        print(f"📊 1 çiftlik, 4 yem türü, 20 hayvan oluşturuldu")

    except Exception as e:
        db.rollback()
        print(f"✗ Hata: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    main()
    """Örnek yemler oluştur"""
    db = get_db_session()

    feeds_data = [
        # Konsantre yemler
        {'name': 'Süt İneği Konsantresi', 'feed_type': 'concentrate', 'cost_per_kg': 8.50, 'dry_matter_percentage': 88.0, 'crude_protein_percentage': 18.0, 'metabolizable_energy_mcal_kg': 2.8, 'storage_life_days': 180, 'moisture_content_percentage': 12.0},
        {'name': 'Besi Konsantresi', 'feed_type': 'concentrate', 'cost_per_kg': 7.80, 'dry_matter_percentage': 87.0, 'crude_protein_percentage': 16.0, 'metabolizable_energy_mcal_kg': 2.7, 'storage_life_days': 180, 'moisture_content_percentage': 13.0},
        {'name': 'Buzağı Başlangıç Yemi', 'feed_type': 'concentrate', 'cost_per_kg': 12.00, 'dry_matter_percentage': 89.0, 'crude_protein_percentage': 22.0, 'metabolizable_energy_mcal_kg': 3.0, 'storage_life_days': 120, 'moisture_content_percentage': 11.0},
        {'name': 'Arpa', 'feed_type': 'grain', 'cost_per_kg': 4.20, 'dry_matter_percentage': 86.0, 'crude_protein_percentage': 11.5, 'metabolizable_energy_mcal_kg': 2.5, 'storage_life_days': 365, 'moisture_content_percentage': 14.0},
        {'name': 'Mısır', 'feed_type': 'grain', 'cost_per_kg': 4.80, 'dry_matter_percentage': 85.0, 'crude_protein_percentage': 9.0, 'metabolizable_energy_mcal_kg': 3.1, 'storage_life_days': 365, 'moisture_content_percentage': 15.0},
        {'name': 'Buğday Kepeği', 'feed_type': 'byproduct', 'cost_per_kg': 3.50, 'dry_matter_percentage': 88.0, 'crude_protein_percentage': 16.0, 'metabolizable_energy_mcal_kg': 2.2, 'storage_life_days': 90, 'moisture_content_percentage': 12.0},
        {'name': 'Soya Küspesi', 'feed_type': 'protein', 'cost_per_kg': 15.00, 'dry_matter_percentage': 89.0, 'crude_protein_percentage': 44.0, 'metabolizable_energy_mcal_kg': 2.4, 'storage_life_days': 180, 'moisture_content_percentage': 11.0},
        {'name': 'Ayçiçeği Küspesi', 'feed_type': 'protein', 'cost_per_kg': 6.50, 'dry_matter_percentage': 90.0, 'crude_protein_percentage': 28.0, 'metabolizable_energy_mcal_kg': 1.9, 'storage_life_days': 120, 'moisture_content_percentage': 10.0},

        # Kaba yemler
        {'name': 'Mısır Silajı', 'feed_type': 'silage', 'cost_per_kg': 1.20, 'dry_matter_percentage': 35.0, 'crude_protein_percentage': 8.0, 'metabolizable_energy_mcal_kg': 2.4, 'storage_life_days': 365, 'moisture_content_percentage': 65.0},
        {'name': 'Çayır Otu Silajı', 'feed_type': 'silage', 'cost_per_kg': 1.00, 'dry_matter_percentage': 30.0, 'crude_protein_percentage': 12.0, 'metabolizable_energy_mcal_kg': 2.0, 'storage_life_days': 365, 'moisture_content_percentage': 70.0},
        {'name': 'Sorgum Silajı', 'feed_type': 'silage', 'cost_per_kg': 1.10, 'dry_matter_percentage': 32.0, 'crude_protein_percentage': 7.5, 'metabolizable_energy_mcal_kg': 2.2, 'storage_life_days': 365, 'moisture_content_percentage': 68.0},
        {'name': 'Yonca Kuru Otu', 'feed_type': 'hay', 'cost_per_kg': 2.80, 'dry_matter_percentage': 88.0, 'crude_protein_percentage': 18.0, 'metabolizable_energy_mcal_kg': 2.2, 'storage_life_days': 730, 'moisture_content_percentage': 12.0},
        {'name': 'Çayır Otu Kuru', 'feed_type': 'hay', 'cost_per_kg': 2.20, 'dry_matter_percentage': 87.0, 'crude_protein_percentage': 10.0, 'metabolizable_energy_mcal_kg': 1.8, 'storage_life_days': 730, 'moisture_content_percentage': 13.0},
        {'name': 'Arpa Samanı', 'feed_type': 'straw', 'cost_per_kg': 0.80, 'dry_matter_percentage': 90.0, 'crude_protein_percentage': 4.0, 'metabolizable_energy_mcal_kg': 1.2, 'storage_life_days': 730, 'moisture_content_percentage': 10.0},
        {'name': 'Buğday Samanı', 'feed_type': 'straw', 'cost_per_kg': 0.70, 'dry_matter_percentage': 91.0, 'crude_protein_percentage': 3.5, 'metabolizable_energy_mcal_kg': 1.1, 'storage_life_days': 730, 'moisture_content_percentage': 9.0},
    ]

    feed_ids = []
    try:
        for farm_id in farm_ids:
            for feed_data in feeds_data:
                feed = Feed(farm_id=farm_id, **feed_data)
                db.add(feed)
                db.flush()
                feed_ids.append(str(feed.id))

        db.commit()
        print(f"✓ {len(feed_ids)} yem kaydı oluşturuldu")
    except Exception as e:
        db.rollback()
        print(f"✗ Yem oluşturma hatası: {e}")
    finally:
        db.close()

    return feed_ids

def create_sample_animals(farm_ids: List[str]) -> List[str]:
    """Örnek hayvanlar oluştur"""
    conn = get_db_connection()
    cursor = conn.cursor()

    breeds = ['holstein', 'angus', 'simmental', 'charolais', 'limousin', 'hereford']
    statuses = ['breeding', 'fattening', 'milking', 'dry', 'calf']

    animal_ids = []

    for farm_id in farm_ids:
        # Her çiftlik için 25-40 hayvan oluştur
        num_animals = random.randint(25, 40)

        for i in range(num_animals):
            animal_id = str(uuid.uuid4())
            animal_ids.append(animal_id)

            breed = random.choice(breeds)
            gender = random.choice(['male', 'female'])

            # Yaş ve ağırlık hesaplama
            age_months = random.randint(6, 120)  # 6 ay - 10 yaş
            birth_date = (datetime.now() - timedelta(days=age_months * 30.44)).strftime('%Y-%m-%d')

            # Irk özelliklerine göre ağırlık
            if breed == 'holstein':
                base_weight = 650 if gender == 'female' else 900
            elif breed == 'angus':
                base_weight = 550 if gender == 'female' else 850
            elif breed == 'simmental':
                base_weight = 750 if gender == 'female' else 1100
            elif breed == 'charolais':
                base_weight = 800 if gender == 'female' else 1200
            elif breed == 'limousin':
                base_weight = 650 if gender == 'female' else 1000
            else:  # hereford
                base_weight = 600 if gender == 'female' else 900

            # Yaşa göre ağırlık ayarlama
            age_factor = min(age_months / 24, 1.0)  # 24 ayda olgun ağırlığa ulaşır
            current_weight = base_weight * (0.3 + 0.7 * age_factor) + random.uniform(-50, 50)
            current_weight = max(current_weight, 50)  # Minimum 50 kg

            # Durum belirleme
            if age_months < 12:
                status = 'calf'
            elif gender == 'female' and breed == 'holstein':
                status = random.choice(['breeding', 'milking', 'dry'])
            elif gender == 'female':
                status = random.choice(['breeding', 'fattening'])
            else:
                status = random.choice(['breeding', 'fattening'])

            # Gebelik durumu (sadece dişiler için)
            is_pregnant = False
            pregnancy_start_date = None
            expected_calving_date = None

            if gender == 'female' and age_months > 15 and random.random() < 0.3:  # %30 gebelik oranı
                is_pregnant = True
                pregnancy_days = random.randint(50, 250)  # Gebelik süresi
                pregnancy_start_date = (datetime.now() - timedelta(days=pregnancy_days)).strftime('%Y-%m-%d')
                expected_calving_date = (datetime.now() + timedelta(days=283 - pregnancy_days)).strftime('%Y-%m-%d')

            # Küpe numarası
            tag = f"{breed.upper()[:3]}-{i+1:03d}"

            # Vücut kondisyon skoru
            body_condition_score = round(random.uniform(2.5, 4.5), 1)

            # Satın alma bilgileri
            purchase_price = random.uniform(8000, 25000) if random.random() < 0.7 else None
            purchase_date = (datetime.now() - timedelta(days=random.randint(30, 1000))).strftime('%Y-%m-%d') if purchase_price else None

            # Notlar
            notes_options = [
                '', 'Sağlıklı hayvan', 'Düzenli kontrol gerekli', 'Yüksek verimli',
                'Özel bakım gerekli', 'Aşı takvimi güncel', 'Beslenme programında'
            ]
            notes = random.choice(notes_options)

            cursor.execute('''
                INSERT INTO animals (
                    id, farm_id, tag, breed, birth_date, gender, current_weight_kg,
                    body_condition_score, status, is_pregnant, pregnancy_start_date,
                    expected_calving_date, purchase_price, purchase_date, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                animal_id, farm_id, tag, breed, birth_date, gender, round(current_weight, 1),
                body_condition_score, status, is_pregnant, pregnancy_start_date,
                expected_calving_date, purchase_price, purchase_date, notes
            ))

    conn.commit()
    conn.close()
    print(f"✓ {len(animal_ids)} hayvan kaydı oluşturuldu")
    return animal_ids

def create_sample_health_data(animal_ids: List[str]):
    """Örnek sağlık verileri oluştur"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Aşı kayıtları oluştur
    vaccines = ['Şap Hastalığı', 'Brucella', 'IBR/BVD', 'Clostridial', 'Anthrax']
    veterinarians = ['Dr. Mehmet Yılmaz', 'Dr. Ayşe Kaya', 'Dr. Ahmet Demir', 'Dr. Fatma Özkan']

    vaccination_count = 0
    for animal_id in animal_ids[:20]:  # İlk 20 hayvan için aşı kayıtları
        for vaccine in vaccines[:random.randint(2, 4)]:  # Her hayvan için 2-4 aşı
            vaccination_id = str(uuid.uuid4())
            vaccination_date = (datetime.now() - timedelta(days=random.randint(30, 365))).strftime('%Y-%m-%d')
            veterinarian = random.choice(veterinarians)
            batch_number = f"LOT{random.randint(1000, 9999)}"
            cost = random.uniform(20, 50)

            cursor.execute('''
                INSERT INTO animal_vaccinations (
                    id, animal_id, vaccine_name, vaccination_date, veterinarian,
                    batch_number, cost, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (vaccination_id, animal_id, vaccine, vaccination_date, veterinarian,
                  batch_number, cost, f'{vaccine} aşısı uygulandı'))
            vaccination_count += 1

    # Sağlık kayıtları oluştur
    health_issues = [
        ('illness', 'Mastitis', 'Antibiyotik tedavisi', 'Penisilin'),
        ('illness', 'Ishal', 'Sıvı tedavisi', 'Elektrolit'),
        ('treatment', 'Topallık', 'Ayak bakımı', 'Antiseptik'),
        ('checkup', 'Rutin Kontrol', 'Genel muayene', ''),
        ('injury', 'Yara', 'Yara bakımı', 'Antiseptik krem'),
    ]

    health_count = 0
    for animal_id in animal_ids[:15]:  # İlk 15 hayvan için sağlık kayıtları
        if random.random() < 0.6:  # %60 olasılık
            record_type, diagnosis, treatment, medication = random.choice(health_issues)
            health_id = str(uuid.uuid4())
            start_date = (datetime.now() - timedelta(days=random.randint(10, 180))).strftime('%Y-%m-%d')
            veterinarian = random.choice(veterinarians)
            cost = random.uniform(50, 300)
            recovery_status = random.choice(['recovered', 'ongoing', 'chronic'])

            cursor.execute('''
                INSERT INTO health_records (
                    id, animal_id, record_type, diagnosis, treatment, medication,
                    veterinarian, cost, start_date, recovery_status, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (health_id, animal_id, record_type, diagnosis, treatment, medication,
                  veterinarian, cost, start_date, recovery_status, f'{diagnosis} tedavisi'))
            health_count += 1

    conn.commit()
    conn.close()
    print(f"✓ {vaccination_count} aşı kaydı ve {health_count} sağlık kaydı oluşturuldu")

def create_sample_breeding_data(animal_ids: List[str]):
    """Örnek üreme verileri oluştur"""
    conn = get_db_connection()
    cursor = conn.cursor()

    # Dişi hayvanları al
    cursor.execute("SELECT id, tag FROM animals WHERE gender = 'female' AND id IN ({})".format(
        ','.join(['?' for _ in animal_ids])), animal_ids)
    female_animals = cursor.fetchall()

    # Erkek hayvanları al
    cursor.execute("SELECT id, tag FROM animals WHERE gender = 'male' AND id IN ({})".format(
        ','.join(['?' for _ in animal_ids])), animal_ids)
    male_animals = cursor.fetchall()

    veterinarians = ['Dr. Mehmet Yılmaz', 'Dr. Ayşe Kaya', 'Dr. Ahmet Demir']
    breeding_count = 0
    calving_count = 0

    for female in female_animals[:10]:  # İlk 10 dişi için üreme kayıtları
        if random.random() < 0.7:  # %70 olasılık
            breeding_id = str(uuid.uuid4())
            breeding_date = (datetime.now() - timedelta(days=random.randint(50, 400))).strftime('%Y-%m-%d')
            breeding_type = random.choice(['natural', 'artificial'])
            male_id = random.choice(male_animals)['id'] if breeding_type == 'natural' and male_animals else None
            semen_source = f"Boğa #{random.randint(100, 999)}" if breeding_type == 'artificial' else ''
            expected_calving_date = (datetime.strptime(breeding_date, '%Y-%m-%d') + timedelta(days=283)).strftime('%Y-%m-%d')
            pregnancy_confirmed = random.choice([True, False, None])
            veterinarian = random.choice(veterinarians)
            cost = random.uniform(100, 500)

            cursor.execute('''
                INSERT INTO breeding_records (
                    id, female_id, male_id, breeding_date, breeding_type, semen_source,
                    expected_calving_date, pregnancy_confirmed, veterinarian, cost, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (breeding_id, female['id'], male_id, breeding_date, breeding_type, semen_source,
                  expected_calving_date, pregnancy_confirmed, veterinarian, cost, 'Çiftleşme kaydı'))
            breeding_count += 1

            # Eğer gebelik onaylandıysa ve tarih geçmişse doğum kaydı oluştur
            if pregnancy_confirmed and datetime.strptime(expected_calving_date, '%Y-%m-%d') < datetime.now():
                calving_id = str(uuid.uuid4())
                calving_date = (datetime.strptime(expected_calving_date, '%Y-%m-%d') + timedelta(days=random.randint(-5, 10))).strftime('%Y-%m-%d')
                calving_ease = random.randint(1, 5)
                birth_weight = random.uniform(25, 45)
                veterinarian_assisted = random.choice([True, False])

                cursor.execute('''
                    INSERT INTO calving_records (
                        id, mother_id, calving_date, calving_ease, birth_weight,
                        veterinarian_assisted, veterinarian, cost, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (calving_id, female['id'], calving_date, calving_ease, birth_weight,
                      veterinarian_assisted, veterinarian if veterinarian_assisted else '',
                      random.uniform(0, 200), 'Doğum kaydı'))
                calving_count += 1

    conn.commit()
    conn.close()
    print(f"✓ {breeding_count} üreme kaydı ve {calving_count} doğum kaydı oluşturuldu")

def create_sample_reminders(animal_ids: List[str]):
    """Örnek hatırlatmalar oluştur"""
    conn = get_db_connection()
    cursor = conn.cursor()

    reminder_types = [
        ('vaccination', 'Aşı Zamanı', 'Yıllık aşı zamanı geldi'),
        ('health_checkup', 'Sağlık Kontrolü', 'Rutin sağlık kontrolü'),
        ('pregnancy_check', 'Gebelik Kontrolü', 'Gebelik kontrolü yapılmalı'),
        ('calving_preparation', 'Doğum Hazırlığı', 'Doğum için hazırlık yapılmalı'),
    ]

    priorities = ['low', 'medium', 'high']
    reminder_count = 0

    for animal_id in animal_ids[:15]:  # İlk 15 hayvan için hatırlatmalar
        num_reminders = random.randint(1, 3)
        for _ in range(num_reminders):
            reminder_id = str(uuid.uuid4())
            reminder_type, title, description = random.choice(reminder_types)
            due_date = (datetime.now() + timedelta(days=random.randint(-10, 30))).strftime('%Y-%m-%d')
            priority = random.choice(priorities)
            is_completed = random.choice([True, False]) if due_date < datetime.now().strftime('%Y-%m-%d') else False

            cursor.execute('''
                INSERT INTO reminders (
                    id, animal_id, reminder_type, title, description, due_date, priority, is_completed
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (reminder_id, animal_id, reminder_type, title, description, due_date, priority, is_completed))
            reminder_count += 1

    conn.commit()
    conn.close()
    print(f"✓ {reminder_count} hatırlatma oluşturuldu")

def main():
    """Ana fonksiyon"""
    print("🚀 Sahte veri oluşturma başlıyor...")

    # Çiftlikler oluştur
    farm_ids = create_sample_farms()

    # Yemler oluştur
    feed_ids = create_sample_feeds(farm_ids)

    # Hayvanlar oluştur
    animal_ids = create_sample_animals(farm_ids)

    # Sağlık verileri oluştur
    create_sample_health_data(animal_ids)

    # Üreme verileri oluştur
    create_sample_breeding_data(animal_ids)

    # Hatırlatmalar oluştur
    create_sample_reminders(animal_ids)

    print("✅ Sahte veri oluşturma tamamlandı!")
    print(f"📊 Toplam: {len(farm_ids)} çiftlik, {len(animal_ids)} hayvan, {len(feed_ids)} yem")

if __name__ == "__main__":
    main()
