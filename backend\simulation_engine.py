"""
<PERSON><PERSON> Simülasyon Motoru
Bilimsel büyüme modelleri ve NRC standartlarına dayalı hesaplamalar
"""

import math
import sqlite3
import uuid
from datetime import datetime
from typing import Dict, Tuple


class LifetimeSimulationEngine:
    """Hayvan yaşam boyu simülasyon motoru"""

    def __init__(self, db_path: str = "hayvancilik.db"):
        self.db_path = db_path

    def get_db_connection(self):
        """Veritabanı bağlantısı"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn

    def get_breed_characteristics(self, breed: str) -> Dict:
        """Irk özelliklerini getir"""
        conn = self.get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM breed_characteristics WHERE breed = ?", (breed,))
        result = cursor.fetchone()
        conn.close()

        if result:
            return dict(result)
        else:
            # <PERSON><PERSON><PERSON><PERSON><PERSON> (<PERSON> i<PERSON>in)
            return {
                'breed': breed,
                'mature_weight_male_kg': 850,
                'mature_weight_female_kg': 550,
                'birth_weight_kg': 35,
                'weaning_weight_kg': 250,
                'yearling_weight_kg': 400,
                'average_daily_gain_kg': 1.2,
                'feed_conversion_ratio': 6.5,
                'age_at_first_calving_months': 24,
                'longevity_years': 12
            }

    def calculate_weight_at_age(self, breed_chars: Dict, gender: str, age_months: int,
                               current_weight: float = None) -> float:
        """Yaşa göre ağırlık hesaplama (Gompertz büyüme modeli)"""

        # Olgun ağırlık
        if gender.lower() == 'male':
            mature_weight = breed_chars['mature_weight_male_kg']
        else:
            mature_weight = breed_chars['mature_weight_female_kg']

        # Doğum ağırlığı
        birth_weight = breed_chars['birth_weight_kg']

        # Gompertz büyüme parametreleri
        # W(t) = A * exp(-B * exp(-k * t))
        # A = olgun ağırlık, B = büyüme hızı, k = büyüme oranı

        A = mature_weight
        B = math.log(A / birth_weight)  # Başlangıç büyüme hızı
        k = 0.08  # Büyüme oranı (aylık)

        # Yaşa göre ağırlık
        weight = A * math.exp(-B * math.exp(-k * age_months))

        # Mevcut ağırlık varsa kullan
        if current_weight and age_months > 0:
            weight = current_weight

        return max(weight, birth_weight)

    def calculate_daily_gain(self, current_weight: float, age_months: int, breed_chars: Dict) -> float:
        """Günlük artış hesaplama"""

        # Yaşa göre büyüme hızı azalır
        base_gain = breed_chars['average_daily_gain_kg']

        # Yaş faktörü (genç hayvanlar daha hızlı büyür)
        if age_months < 6:
            age_factor = 1.2  # %20 daha hızlı
        elif age_months < 12:
            age_factor = 1.0  # Normal
        elif age_months < 24:
            age_factor = 0.8  # %20 daha yavaş
        else:
            age_factor = 0.3  # Çok yavaş (olgun hayvan)

        # Ağırlık faktörü (olgun ağırlığa yaklaştıkça yavaşlar)
        mature_weight = breed_chars['mature_weight_male_kg']  # En yüksek ağırlık referans
        weight_ratio = current_weight / mature_weight

        if weight_ratio < 0.3:
            weight_factor = 1.2  # Genç, hızlı büyüme
        elif weight_ratio < 0.6:
            weight_factor = 1.0  # Normal büyüme
        elif weight_ratio < 0.8:
            weight_factor = 0.7  # Yavaşlayan büyüme
        else:
            weight_factor = 0.2  # Çok yavaş büyüme

        daily_gain = base_gain * age_factor * weight_factor

        # Minimum ve maksimum sınırlar
        return max(0.1, min(daily_gain, 2.0))

    def calculate_feed_consumption(self, weight_kg: float, daily_gain_kg: float,
                                 age_months: int, is_pregnant: bool = False,
                                 is_lactating: bool = False) -> float:
        """Günlük yem tüketimi hesaplama (NRC standartları)"""

        # Temel kuru madde alımı (vücut ağırlığının %2-3'ü)
        base_dmi_percentage = 2.5

        # Yaş faktörü
        if age_months < 6:
            age_factor = 1.1  # Genç hayvanlar daha fazla yer
        elif age_months < 12:
            age_factor = 1.05
        else:
            age_factor = 1.0

        # Büyüme faktörü
        if daily_gain_kg > 1.0:
            growth_factor = 1.15  # Hızlı büyüyen hayvanlar daha fazla yer
        elif daily_gain_kg > 0.5:
            growth_factor = 1.05
        else:
            growth_factor = 1.0

        # Fizyolojik durum faktörleri
        pregnancy_factor = 1.15 if is_pregnant else 1.0
        lactation_factor = 1.25 if is_lactating else 1.0

        # Toplam DMI hesaplama
        dmi_percentage = base_dmi_percentage * age_factor * growth_factor * pregnancy_factor * lactation_factor
        daily_consumption = weight_kg * (dmi_percentage / 100)

        return round(daily_consumption, 2)

    def determine_physiological_stage(self, age_months: int, gender: str,
                                    breed_chars: Dict) -> Tuple[str, bool, bool]:
        """Fizyolojik dönem belirleme"""

        is_pregnant = False
        is_lactating = False

        if age_months < 2:
            stage = "suckling"  # Emziren buzağı
        elif age_months < 6:
            stage = "weaning"   # Sütten kesim
        elif age_months < 12:
            stage = "growing"   # Büyüme
        elif age_months < 18:
            stage = "yearling"  # Düve/tosun
        else:
            if gender.lower() == 'female':
                first_calving_age = breed_chars.get('age_at_first_calving_months', 24)
                if age_months >= first_calving_age:
                    stage = "breeding"  # Üreme dönemi
                    # Gebelik ve laktasyon döngüsü (12 aylık)
                    cycle_month = (age_months - first_calving_age) % 12
                    if cycle_month < 9:  # 9 ay gebelik
                        is_pregnant = True
                    if cycle_month >= 9 or cycle_month < 2:  # Doğum sonrası 2 ay laktasyon
                        is_lactating = True
                else:
                    stage = "heifer"    # Gebe olmamış düve
            else:
                stage = "bull"      # Boğa

        return stage, is_pregnant, is_lactating

    def calculate_feed_cost_per_kg(self, farm_id: str) -> float:
        """Çiftlik için ortalama yem maliyeti hesaplama"""
        conn = self.get_db_connection()
        cursor = conn.cursor()

        # Çiftlikteki yemlerin ağırlıklı ortalaması
        cursor.execute("""
            SELECT AVG(cost_per_kg) as avg_cost
            FROM feeds
            WHERE farm_id = ?
        """, (farm_id,))

        result = cursor.fetchone()
        conn.close()

        if result and result['avg_cost']:
            return float(result['avg_cost'])
        else:
            return 2.5  # Varsayılan maliyet (TL/kg)

    def run_lifetime_simulation(self, animal_id: str, farm_id: str,
                              simulation_name: str, end_age_months: int = 60,
                              slaughter_age_months: int = None) -> str:
        """Yaşam boyu simülasyon çalıştır"""

        conn = self.get_db_connection()
        cursor = conn.cursor()

        # Hayvan bilgilerini al
        cursor.execute("SELECT * FROM animals WHERE id = ?", (animal_id,))
        animal = cursor.fetchone()

        if not animal:
            raise ValueError("Hayvan bulunamadı")

        animal_dict = dict(animal)

        # Yaş hesaplama
        birth_date = datetime.strptime(animal_dict['birth_date'], '%Y-%m-%d')
        current_age_months = int((datetime.now() - birth_date).days / 30.44)

        # Irk özelliklerini al
        breed_chars = self.get_breed_characteristics(animal_dict['breed'])

        # Ortalama yem maliyeti
        avg_feed_cost_per_kg = self.calculate_feed_cost_per_kg(farm_id)

        # Simülasyon kaydı oluştur
        simulation_id = str(uuid.uuid4())

        # Simülasyon hesaplamaları
        total_feed_cost = 0
        total_feed_consumption = 0
        simulation_details = []

        current_weight = animal_dict['current_weight_kg']

        for month in range(current_age_months, end_age_months + 1):
            # Fizyolojik dönem belirleme
            stage, is_pregnant, is_lactating = self.determine_physiological_stage(
                month, animal_dict['gender'], breed_chars
            )

            # Günlük artış hesaplama
            daily_gain = self.calculate_daily_gain(
                current_weight, month, breed_chars
            )

            # Günlük yem tüketimi
            daily_consumption = self.calculate_feed_consumption(
                current_weight, daily_gain, month, is_pregnant, is_lactating
            )

            # Aylık hesaplamalar
            monthly_consumption = daily_consumption * 30.44  # Ortalama ay günü
            monthly_cost = monthly_consumption * avg_feed_cost_per_kg

            # Toplamları güncelle
            total_feed_consumption += monthly_consumption
            total_feed_cost += monthly_cost

            # Detay kaydı
            detail_id = str(uuid.uuid4())
            simulation_details.append({
                'id': detail_id,
                'simulation_id': simulation_id,
                'month': month - current_age_months + 1,
                'age_months': month,
                'weight_kg': round(current_weight, 1),
                'daily_gain_kg': round(daily_gain, 3),
                'feed_consumption_kg_per_day': round(daily_consumption, 2),
                'monthly_feed_cost': round(monthly_cost, 2),
                'monthly_feed_consumption_kg': round(monthly_consumption, 1),
                'physiological_stage': stage,
                'is_pregnant': is_pregnant,
                'is_lactating': is_lactating
            })

            # Ağırlığı güncelle
            current_weight += daily_gain * 30.44

            # Kesim yaşına ulaştıysa dur
            if slaughter_age_months and month >= slaughter_age_months:
                break

        # Ana simülasyon kaydını veritabanına ekle
        cursor.execute('''
            INSERT INTO simulations (
                id, farm_id, animal_id, name, simulation_type,
                start_age_months, end_age_months, slaughter_age_months,
                total_feed_cost, total_feed_consumption_kg
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            simulation_id, farm_id, animal_id, simulation_name, 'lifetime',
            current_age_months, end_age_months, slaughter_age_months,
            round(total_feed_cost, 2), round(total_feed_consumption, 1)
        ))

        # Detay kayıtlarını ekle
        for detail in simulation_details:
            cursor.execute('''
                INSERT INTO simulation_details (
                    id, simulation_id, month, age_months, weight_kg, daily_gain_kg,
                    feed_consumption_kg_per_day, monthly_feed_cost, monthly_feed_consumption_kg,
                    physiological_stage, is_pregnant, is_lactating
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                detail['id'], detail['simulation_id'], detail['month'], detail['age_months'],
                detail['weight_kg'], detail['daily_gain_kg'], detail['feed_consumption_kg_per_day'],
                detail['monthly_feed_cost'], detail['monthly_feed_consumption_kg'],
                detail['physiological_stage'], detail['is_pregnant'], detail['is_lactating']
            ))

        conn.commit()
        conn.close()

        return simulation_id
