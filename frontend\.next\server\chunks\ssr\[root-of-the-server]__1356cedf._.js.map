{"version": 3, "sources": [], "sections": [{"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard hatırlatmaları\n  getReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/reminders/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health Management API\nexport const healthApi = {\n  // Aşı takvimi getir\n  getVaccineSchedule: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/health/vaccines/schedule/${animalId}`);\n    return response.data;\n  },\n\n  // Aşı kaydı oluştur\n  recordVaccination: async (request: any): Promise<any> => {\n    const response = await api.post('/health/vaccines/record', request);\n    return response.data;\n  },\n\n  // Sağlık kayıtlarını getir\n  getHealthRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/health/records/${animalId}`);\n    return response.data;\n  },\n\n  // Sağlık kaydı oluştur\n  createHealthRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/health/records', request);\n    return response.data;\n  },\n};\n\n// Breeding Management API\nexport const breedingApi = {\n  // Üreme kayıtlarını getir\n  getBreedingRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/breeding/records/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftleşme kaydı oluştur\n  createBreedingRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/records', request);\n    return response.data;\n  },\n\n  // Gebelik durumunu güncelle\n  updatePregnancyStatus: async (breedingId: string, request: any): Promise<any> => {\n    const response = await api.put(`/breeding/pregnancy/${breedingId}`, request);\n    return response.data;\n  },\n\n  // Doğum kaydı oluştur\n  recordCalving: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/calving', request);\n    return response.data;\n  },\n\n  // Üreme takvimi getir\n  getBreedingCalendar: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/breeding/calendar/${farmId}`);\n    return response.data;\n  },\n};\n\n// Reminder System API\nexport const reminderApi = {\n  // Çiftlik hatırlatmalarını getir\n  getFarmReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/reminders/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Hatırlatmayı tamamla\n  completeReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.put(`/reminders/${reminderId}/complete`);\n    return response.data;\n  },\n\n  // Manuel hatırlatma oluştur\n  createReminder: async (request: any): Promise<any> => {\n    const response = await api.post('/reminders', request);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthCheckApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,oBAAoB;IACpB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,mBAAmB,OAAO;QACxB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,0BAA0B;IAC1B,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,uBAAuB,OAAO,YAAoB;QAChD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ;QAC7D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,iCAAiC;IACjC,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,SAAS,CAAC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/simulation/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { \n  TrendingUp, \n  Plus, \n  Eye, \n  Trash2, \n  Calculator,\n  BarChart3,\n  Clock,\n  DollarSign,\n  Target,\n  AlertCircle\n} from 'lucide-react';\nimport { simulationApi, farmApi } from '@/services/api';\nimport { Simulation, Farm } from '@/types';\n\nexport default function SimulationPage() {\n  const [simulations, setSimulations] = useState<Simulation[]>([]);\n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [selectedFarmId, setSelectedFarmId] = useState<string>('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  useEffect(() => {\n    if (selectedFarmId) {\n      loadSimulations();\n    }\n  }, [selectedFarmId]);\n\n  const loadFarms = async () => {\n    try {\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n      if (farmsData.length > 0) {\n        setSelectedFarmId(farmsData[0].id);\n      }\n    } catch (err) {\n      setError('Çiftlikler yüklenirken hata oluştu');\n      console.error(err);\n    }\n  };\n\n  const loadSimulations = async () => {\n    if (!selectedFarmId) return;\n    \n    setLoading(true);\n    try {\n      const simulationsData = await simulationApi.getSimulationsByFarm(selectedFarmId);\n      setSimulations(simulationsData);\n    } catch (err) {\n      setError('Simülasyonlar yüklenirken hata oluştu');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteSimulation = async (simulationId: string) => {\n    if (!confirm('Bu simülasyonu silmek istediğinizden emin misiniz?')) return;\n\n    try {\n      await simulationApi.deleteSimulation(simulationId);\n      await loadSimulations();\n    } catch (err) {\n      setError('Simülasyon silinirken hata oluştu');\n      console.error(err);\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('tr-TR', {\n      style: 'currency',\n      currency: 'TRY'\n    }).format(amount);\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('tr-TR', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const getSimulationDuration = (startAge: number, endAge: number) => {\n    const months = endAge - startAge;\n    const years = Math.floor(months / 12);\n    const remainingMonths = months % 12;\n    \n    if (years > 0) {\n      return `${years} yıl ${remainingMonths > 0 ? `${remainingMonths} ay` : ''}`;\n    }\n    return `${months} ay`;\n  };\n\n  if (loading && !selectedFarmId) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Yükleniyor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Yaşam Boyu Simülasyon</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Hayvanlarınızın yaşam boyu yem tüketimi ve maliyetini analiz edin\n          </p>\n        </div>\n        <Link\n          href=\"/simulation/create\"\n          className=\"btn-primary text-white px-6 py-3 rounded-lg flex items-center space-x-2 hover-glow\"\n        >\n          <Plus className=\"h-5 w-5\" />\n          <span>Yeni Simülasyon</span>\n        </Link>\n      </div>\n\n      {/* Farm Selection */}\n      {farms.length > 0 && (\n        <div className=\"content-overlay p-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Çiftlik Seçin\n          </label>\n          <select\n            value={selectedFarmId}\n            onChange={(e) => setSelectedFarmId(e.target.value)}\n            className=\"w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n          >\n            {farms.map((farm) => (\n              <option key={farm.id} value={farm.id}>\n                {farm.name} - {farm.location}\n              </option>\n            ))}\n          </select>\n        </div>\n      )}\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Simulations List */}\n      {selectedFarmId && (\n        <div className=\"space-y-4\">\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4\"></div>\n              <p className=\"text-gray-600\">Simülasyonlar yükleniyor...</p>\n            </div>\n          ) : simulations.length === 0 ? (\n            <div className=\"content-overlay p-8 text-center\">\n              <TrendingUp className=\"h-16 w-16 text-gray-400 mx-auto mb-4\" />\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n                Henüz simülasyon bulunmuyor\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                Hayvanlarınız için yaşam boyu simülasyon oluşturmaya başlayın\n              </p>\n              <Link\n                href=\"/simulation/create\"\n                className=\"btn-primary text-white px-6 py-3 rounded-lg inline-flex items-center space-x-2\"\n              >\n                <Plus className=\"h-5 w-5\" />\n                <span>İlk Simülasyonu Oluştur</span>\n              </Link>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              {simulations.map((simulation) => (\n                <div\n                  key={simulation.id}\n                  className=\"content-overlay hover-glow card-animate p-6\"\n                >\n                  {/* Header */}\n                  <div className=\"flex justify-between items-start mb-4\">\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-gray-900\">\n                        {simulation.name}\n                      </h3>\n                      <p className=\"text-sm text-gray-600\">\n                        {simulation.breed} - {simulation.gender === 'male' ? 'Erkek' : 'Dişi'}\n                      </p>\n                    </div>\n                    <div className=\"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium\">\n                      Yaşam Boyu\n                    </div>\n                  </div>\n\n                  {/* Stats */}\n                  <div className=\"space-y-3 mb-4\">\n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-2\">\n                        <Clock className=\"h-4 w-4 text-gray-500\" />\n                        <span className=\"text-sm text-gray-600\">Süre:</span>\n                      </div>\n                      <span className=\"font-medium text-gray-900\">\n                        {getSimulationDuration(simulation.start_age_months, simulation.end_age_months)}\n                      </span>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-2\">\n                        <DollarSign className=\"h-4 w-4 text-gray-500\" />\n                        <span className=\"text-sm text-gray-600\">Toplam Maliyet:</span>\n                      </div>\n                      <span className=\"font-medium text-gray-900\">\n                        {formatCurrency(simulation.total_feed_cost)}\n                      </span>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-2\">\n                        <Target className=\"h-4 w-4 text-gray-500\" />\n                        <span className=\"text-sm text-gray-600\">Toplam Yem:</span>\n                      </div>\n                      <span className=\"font-medium text-gray-900\">\n                        {simulation.total_feed_consumption_kg.toLocaleString('tr-TR')} kg\n                      </span>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center space-x-2\">\n                        <BarChart3 className=\"h-4 w-4 text-gray-500\" />\n                        <span className=\"text-sm text-gray-600\">Yaş Aralığı:</span>\n                      </div>\n                      <span className=\"font-medium text-gray-900\">\n                        {simulation.start_age_months} - {simulation.end_age_months} ay\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Date */}\n                  <div className=\"text-xs text-gray-500 mb-4\">\n                    Oluşturulma: {formatDate(simulation.created_at)}\n                  </div>\n\n                  {/* Actions */}\n                  <div className=\"flex space-x-2\">\n                    <Link\n                      href={`/simulation/${simulation.id}`}\n                      className=\"flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-100 transition-colors flex items-center justify-center space-x-1\"\n                    >\n                      <Eye className=\"h-4 w-4\" />\n                      <span>Detay</span>\n                    </Link>\n                    \n                    <button\n                      onClick={() => handleDeleteSimulation(simulation.id)}\n                      className=\"bg-red-50 text-red-600 px-3 py-2 rounded-md text-sm font-medium hover:bg-red-100 transition-colors flex items-center justify-center\"\n                    >\n                      <Trash2 className=\"h-4 w-4\" />\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAhBA;;;;;;AAmBe,SAAS;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB;YAClB;QACF;IACF,GAAG;QAAC;KAAe;IAEnB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,sHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;YACT,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,kBAAkB,SAAS,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,gBAAgB;QAErB,WAAW;QACX,IAAI;YACF,MAAM,kBAAkB,MAAM,sHAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC;YACjE,eAAe;QACjB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI,CAAC,QAAQ,uDAAuD;QAEpE,IAAI;YACF,MAAM,sHAAA,CAAA,gBAAa,CAAC,gBAAgB,CAAC;YACrC,MAAM;QACR,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,MAAM,SAAS,SAAS;QACxB,MAAM,QAAQ,KAAK,KAAK,CAAC,SAAS;QAClC,MAAM,kBAAkB,SAAS;QAEjC,IAAI,QAAQ,GAAG;YACb,OAAO,GAAG,MAAM,KAAK,EAAE,kBAAkB,IAAI,GAAG,gBAAgB,GAAG,CAAC,GAAG,IAAI;QAC7E;QACA,OAAO,GAAG,OAAO,GAAG,CAAC;IACvB;IAEA,IAAI,WAAW,CAAC,gBAAgB;QAC9B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAKT,MAAM,MAAM,GAAG,mBACd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,8OAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;gCAAqB,OAAO,KAAK,EAAE;;oCACjC,KAAK,IAAI;oCAAC;oCAAI,KAAK,QAAQ;;+BADjB,KAAK,EAAE;;;;;;;;;;;;;;;;YAS3B,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;YAO5C,gCACC,8OAAC;gBAAI,WAAU;0BACZ,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;2BAE7B,YAAY,MAAM,KAAK,kBACzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;yCAIV,8OAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,8OAAC;4BAEC,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DACX,WAAW,IAAI;;;;;;8DAElB,8OAAC;oDAAE,WAAU;;wDACV,WAAW,KAAK;wDAAC;wDAAI,WAAW,MAAM,KAAK,SAAS,UAAU;;;;;;;;;;;;;sDAGnE,8OAAC;4CAAI,WAAU;sDAAuE;;;;;;;;;;;;8CAMxF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAE1C,8OAAC;oDAAK,WAAU;8DACb,sBAAsB,WAAW,gBAAgB,EAAE,WAAW,cAAc;;;;;;;;;;;;sDAIjF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAE1C,8OAAC;oDAAK,WAAU;8DACb,eAAe,WAAW,eAAe;;;;;;;;;;;;sDAI9C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAE1C,8OAAC;oDAAK,WAAU;;wDACb,WAAW,yBAAyB,CAAC,cAAc,CAAC;wDAAS;;;;;;;;;;;;;sDAIlE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kNAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;;8DAE1C,8OAAC;oDAAK,WAAU;;wDACb,WAAW,gBAAgB;wDAAC;wDAAI,WAAW,cAAc;wDAAC;;;;;;;;;;;;;;;;;;;8CAMjE,8OAAC;oCAAI,WAAU;;wCAA6B;wCAC5B,WAAW,WAAW,UAAU;;;;;;;8CAIhD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,YAAY,EAAE,WAAW,EAAE,EAAE;4CACpC,WAAU;;8DAEV,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAGR,8OAAC;4CACC,SAAS,IAAM,uBAAuB,WAAW,EAAE;4CACnD,WAAU;sDAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;2BAhFjB,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;AA2FpC", "debugId": null}}]}