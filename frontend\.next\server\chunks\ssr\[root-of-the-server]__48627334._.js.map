{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport {\n  Home,\n  Building2,\n  Beef,\n  Wheat,\n  Calculator,\n  BarChart3,\n  FileText,\n  TrendingUp\n} from 'lucide-react';\n\nconst Navigation = () => {\n  const pathname = usePathname();\n\n  const navItems = [\n    {\n      name: '<PERSON> Sayfa',\n      href: '/',\n      icon: Home,\n    },\n    {\n      name: 'Dashboard',\n      href: '/dashboard',\n      icon: BarChart3,\n    },\n    {\n      name: 'Çiftlik Yönetimi',\n      href: '/farms',\n      icon: Building2,\n    },\n    {\n      name: '<PERSON><PERSON>',\n      href: '/animals',\n      icon: Beef,\n    },\n    {\n      name: '<PERSON><PERSON>',\n      href: '/feeds',\n      icon: Wheat,\n    },\n    {\n      name: '<PERSON><PERSON><PERSON>lam<PERSON>',\n      href: '/rations',\n      icon: Calculator,\n    },\n    {\n      name: 'Ya<PERSON><PERSON>u Simülasyon',\n      href: '/simulation',\n      icon: TrendingUp,\n    },\n    {\n      name: '<PERSON><PERSON><PERSON>',\n      href: '/reports',\n      icon: FileText,\n    },\n  ];\n\n  return (\n    <nav className=\"nav-glass shadow-lg border-b border-white/20\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <Beef className=\"h-8 w-8 text-green-600\" />\n            <span className=\"text-xl font-bold text-gray-800\">\n              Hayvan Yetiştiriciliği\n            </span>\n          </div>\n\n          {/* Navigation Links */}\n          <div className=\"hidden md:flex items-center space-x-1\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    isActive\n                      ? 'bg-green-100 text-green-700'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                  }`}\n                >\n                  <Icon className=\"h-4 w-4\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              type=\"button\"\n              className=\"text-gray-600 hover:text-gray-900 focus:outline-none focus:text-gray-900\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div className=\"md:hidden\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium transition-colors ${\n                    isActive\n                      ? 'bg-green-100 text-green-700'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                  }`}\n                >\n                  <Icon className=\"h-5 w-5\" />\n                  <span>{item.name}</span>\n                </Link>\n              );\n            })}\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAeA,MAAM,aAAa;IACjB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,WAAW;QACf;YACE,MAAM;YACN,MAAM;YACN,MAAM,mMAAA,CAAA,OAAI;QACZ;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,YAAS;QACjB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,gNAAA,CAAA,YAAS;QACjB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;QACZ;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,oMAAA,CAAA,QAAK;QACb;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,aAAU;QAClB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,aAAU;QAClB;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,8MAAA,CAAA,WAAQ;QAChB;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAMpD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAC,uFAAuF,EACjG,WACI,gCACA,uDACJ;;sDAEF,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCATX,KAAK,IAAI;;;;;4BAYpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC9D,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7E,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,OAAO,KAAK,IAAI;4BACtB,MAAM,WAAW,aAAa,KAAK,IAAI;4BAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,yFAAyF,EACnG,WACI,gCACA,uDACJ;;kDAEF,8OAAC;wCAAK,WAAU;;;;;;kDAChB,8OAAC;kDAAM,KAAK,IAAI;;;;;;;+BATX,KAAK,IAAI;;;;;wBAYpB;;;;;;;;;;;;;;;;;;;;;;AAMZ;uCAEe", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/components/MouseFollower.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\nconst MouseFollower = () => {\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({ x: e.clientX, y: e.clientY });\n      setIsVisible(true);\n    };\n\n    const handleMouseLeave = () => {\n      setIsVisible(false);\n    };\n\n    document.addEventListener('mousemove', handleMouseMove);\n    document.addEventListener('mouseleave', handleMouseLeave);\n\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseleave', handleMouseLeave);\n    };\n  }, []);\n\n  if (!isVisible) return null;\n\n  return (\n    <div\n      className=\"mouse-follower\"\n      style={{\n        left: mousePosition.x - 12,\n        top: mousePosition.y - 12,\n      }}\n    />\n  );\n};\n\nexport default MouseFollower;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,gBAAgB;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,iBAAiB;gBAAE,GAAG,EAAE,OAAO;gBAAE,GAAG,EAAE,OAAO;YAAC;YAC9C,aAAa;QACf;QAEA,MAAM,mBAAmB;YACvB,aAAa;QACf;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,SAAS,gBAAgB,CAAC,cAAc;QAExC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,cAAc;QAC7C;IACF,GAAG,EAAE;IAEL,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,MAAM,cAAc,CAAC,GAAG;YACxB,KAAK,cAAc,CAAC,GAAG;QACzB;;;;;;AAGN;uCAEe", "debugId": null}}]}