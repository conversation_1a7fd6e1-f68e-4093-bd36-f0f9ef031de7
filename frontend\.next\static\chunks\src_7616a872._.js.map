{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/services/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { Farm, FarmCreate, Animal, AnimalCreate, AnimalStats, Feed } from '@/types';\n\nconst API_BASE_URL = 'http://localhost:8000/api';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Farm API\nexport const farmApi = {\n  // Tüm çiftlikleri getir\n  getFarms: async (): Promise<Farm[]> => {\n    const response = await api.get('/farms/');\n    return response.data;\n  },\n\n  // Belirli bir çiftliği getir\n  getFarm: async (farmId: string): Promise<Farm> => {\n    const response = await api.get(`/farms/${farmId}`);\n    return response.data;\n  },\n\n  // Yeni çiftlik oluştur\n  createFarm: async (farmData: FarmCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/farms/', farmData);\n    return response.data;\n  },\n\n  // Çiftlik güncelle\n  updateFarm: async (farmId: string, farmData: FarmCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/farms/${farmId}`, farmData);\n    return response.data;\n  },\n\n  // Çiftlik sil\n  deleteFarm: async (farmId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/farms/${farmId}`);\n    return response.data;\n  },\n};\n\n// Animal API\nexport const animalApi = {\n  // Çiftlikteki hayvanları getir\n  getAnimalsByFarm: async (farmId: string): Promise<Animal[]> => {\n    const response = await api.get(`/animals/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir hayvanı getir\n  getAnimal: async (animalId: string): Promise<Animal> => {\n    const response = await api.get(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Yeni hayvan ekle\n  createAnimal: async (animalData: AnimalCreate): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/animals/', animalData);\n    return response.data;\n  },\n\n  // Hayvan güncelle\n  updateAnimal: async (animalId: string, animalData: AnimalCreate): Promise<{ message: string }> => {\n    const response = await api.put(`/animals/${animalId}`, animalData);\n    return response.data;\n  },\n\n  // Hayvan sil\n  deleteAnimal: async (animalId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/animals/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftlik hayvan istatistikleri\n  getFarmAnimalStats: async (farmId: string): Promise<AnimalStats> => {\n    const response = await api.get(`/animals/farm/${farmId}/stats`);\n    return response.data;\n  },\n};\n\n// Feed API\nexport const feedApi = {\n  // Çiftlikteki yemleri getir\n  getFeedsByFarm: async (farmId: string): Promise<Feed[]> => {\n    const response = await api.get(`/feeds/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Belirli bir yemi getir\n  getFeed: async (feedId: string): Promise<Feed> => {\n    const response = await api.get(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yeni yem ekle\n  createFeed: async (feedData: any): Promise<{ id: string; message: string }> => {\n    const response = await api.post('/feeds/', feedData);\n    return response.data;\n  },\n\n  // Yem güncelle\n  updateFeed: async (feedId: string, feedData: any): Promise<{ message: string }> => {\n    const response = await api.put(`/feeds/${feedId}`, feedData);\n    return response.data;\n  },\n\n  // Yem sil\n  deleteFeed: async (feedId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/feeds/${feedId}`);\n    return response.data;\n  },\n\n  // Yem türlerini getir\n  getFeedTypesByFarm: async (farmId: string): Promise<Record<string, any[]>> => {\n    const response = await api.get(`/feeds/farm/${farmId}/types`);\n    return response.data;\n  },\n\n  // Örnek yem verileri ekle\n  addSampleFeeds: async (farmId: string): Promise<{ message: string; feeds: string[] }> => {\n    const response = await api.post(`/feeds/farm/${farmId}/sample-feeds`);\n    return response.data;\n  },\n};\n\n// Ration API\nexport const rationApi = {\n  // Besin ihtiyaçlarını hesapla\n  calculateRequirements: async (animalId: string): Promise<any> => {\n    const response = await api.post(`/rations/calculate-requirements?animal_id=${animalId}`);\n    return response.data;\n  },\n\n  // Rasyon optimizasyonu yap\n  optimizeRation: async (request: any): Promise<any> => {\n    const response = await api.post('/rations/optimize', request);\n    return response.data;\n  },\n\n  // Çiftlikteki rasyonları getir\n  getRationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/rations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Rasyon detaylarını getir\n  getRationDetails: async (rationId: string): Promise<any> => {\n    const response = await api.get(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Rasyonu aktif hale getir\n  activateRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.put(`/rations/${rationId}/activate`);\n    return response.data;\n  },\n\n  // Rasyonu sil\n  deleteRation: async (rationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/rations/${rationId}`);\n    return response.data;\n  },\n\n  // Hayvan için aktif rasyonu getir\n  getActiveRationForAnimal: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/rations/animal/${animalId}/active`);\n    return response.data;\n  },\n};\n\n// Simulation API\nexport const simulationApi = {\n  // Simülasyon çalıştır\n  runSimulation: async (request: any): Promise<any> => {\n    const response = await api.post('/simulations/run', request);\n    return response.data;\n  },\n\n  // Çiftlikteki simülasyonları getir\n  getSimulationsByFarm: async (farmId: string): Promise<any[]> => {\n    const response = await api.get(`/simulations/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Simülasyon detaylarını getir\n  getSimulationDetails: async (simulationId: string): Promise<any> => {\n    const response = await api.get(`/simulations/${simulationId}`);\n    return response.data;\n  },\n\n  // Simülasyonu sil\n  deleteSimulation: async (simulationId: string): Promise<{ message: string }> => {\n    const response = await api.delete(`/simulations/${simulationId}`);\n    return response.data;\n  },\n};\n\n// Dashboard API\nexport const dashboardApi = {\n  // Dashboard genel bakış\n  getOverview: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/overview/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard finansal veriler\n  getFinancial: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/financial/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard performans verileri\n  getPerformance: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/performance/${farmId}`);\n    return response.data;\n  },\n\n  // Dashboard hatırlatmaları\n  getReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/dashboard/reminders/${farmId}`);\n    return response.data;\n  },\n};\n\n// Health Management API\nexport const healthApi = {\n  // Aşı takvimi getir\n  getVaccineSchedule: async (animalId: string): Promise<any> => {\n    const response = await api.get(`/health/vaccines/schedule/${animalId}`);\n    return response.data;\n  },\n\n  // Aşı kaydı oluştur\n  recordVaccination: async (request: any): Promise<any> => {\n    const response = await api.post('/health/vaccines/record', request);\n    return response.data;\n  },\n\n  // Sağlık kayıtlarını getir\n  getHealthRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/health/records/${animalId}`);\n    return response.data;\n  },\n\n  // Sağlık kaydı oluştur\n  createHealthRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/health/records', request);\n    return response.data;\n  },\n};\n\n// Breeding Management API\nexport const breedingApi = {\n  // Üreme kayıtlarını getir\n  getBreedingRecords: async (animalId: string): Promise<any[]> => {\n    const response = await api.get(`/breeding/records/${animalId}`);\n    return response.data;\n  },\n\n  // Çiftleşme kaydı oluştur\n  createBreedingRecord: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/records', request);\n    return response.data;\n  },\n\n  // Gebelik durumunu güncelle\n  updatePregnancyStatus: async (breedingId: string, request: any): Promise<any> => {\n    const response = await api.put(`/breeding/pregnancy/${breedingId}`, request);\n    return response.data;\n  },\n\n  // Doğum kaydı oluştur\n  recordCalving: async (request: any): Promise<any> => {\n    const response = await api.post('/breeding/calving', request);\n    return response.data;\n  },\n\n  // Üreme takvimi getir\n  getBreedingCalendar: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/breeding/calendar/${farmId}`);\n    return response.data;\n  },\n};\n\n// Reminder System API\nexport const reminderApi = {\n  // Çiftlik hatırlatmalarını getir\n  getFarmReminders: async (farmId: string): Promise<any> => {\n    const response = await api.get(`/reminders/farm/${farmId}`);\n    return response.data;\n  },\n\n  // Hatırlatmayı tamamla\n  completeReminder: async (reminderId: string): Promise<any> => {\n    const response = await api.put(`/reminders/${reminderId}/complete`);\n    return response.data;\n  },\n\n  // Manuel hatırlatma oluştur\n  createReminder: async (request: any): Promise<any> => {\n    const response = await api.post('/reminders', request);\n    return response.data;\n  },\n};\n\n// Health check\nexport const healthCheckApi = {\n  checkHealth: async (): Promise<{ status: string }> => {\n    const response = await api.get('/health', { baseURL: 'http://localhost:8000' });\n    return response.data;\n  },\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AAGA,MAAM,eAAe;AAErB,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAGO,MAAM,UAAU;IACrB,wBAAwB;IACxB,UAAU;QACR,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,mBAAmB;IACnB,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,aAAa;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,cAAc,OAAO,UAAkB;QACrC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU,EAAE;QACvD,OAAO,SAAS,IAAI;IACtB;IAEA,aAAa;IACb,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,SAAS,OAAO;QACd,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,WAAW;QAC3C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,YAAY,OAAO,QAAgB;QACjC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,EAAE,QAAQ,EAAE;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,UAAU;IACV,YAAY,OAAO;QACjB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,OAAO,EAAE,QAAQ;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,MAAM,CAAC;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;QACpE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,8BAA8B;IAC9B,uBAAuB,OAAO;QAC5B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,CAAC,0CAA0C,EAAE,UAAU;QACvF,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,cAAc,EAAE,QAAQ;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,UAAU;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,SAAS,EAAE,UAAU;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,kCAAkC;IAClC,0BAA0B,OAAO;QAC/B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,SAAS,OAAO,CAAC;QACnE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,oBAAoB;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,mCAAmC;IACnC,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,aAAa,EAAE,cAAc;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,eAAe;IAC1B,wBAAwB;IACxB,aAAa,OAAO;QAClB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,cAAc,OAAO;QACnB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,qBAAqB,EAAE,QAAQ;QAC/D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,YAAY;IACvB,oBAAoB;IACpB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,0BAA0B,EAAE,UAAU;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,mBAAmB,OAAO;QACxB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,2BAA2B;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,mBAAmB;QACnD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,0BAA0B;IAC1B,oBAAoB,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,sBAAsB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,uBAAuB,OAAO,YAAoB;QAChD,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,qBAAqB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,qBAAqB,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,mBAAmB,EAAE,QAAQ;QAC7D,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc;IACzB,iCAAiC;IACjC,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,gBAAgB,EAAE,QAAQ;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,kBAAkB,OAAO;QACvB,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,SAAS,CAAC;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,gBAAgB,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,IAAI,CAAC,cAAc;QAC9C,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,iBAAiB;IAC5B,aAAa;QACX,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,WAAW;YAAE,SAAS;QAAwB;QAC7E,OAAO,SAAS,IAAI;IACtB;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Vscode/hayvancilik/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport {\n  BarChart3,\n  TrendingUp,\n  DollarSign,\n  Users,\n  Target,\n  AlertCircle,\n  Activity,\n  Calendar,\n  PieChart,\n  ArrowUp,\n  ArrowDown\n} from 'lucide-react';\nimport { dashboardApi, farmApi } from '@/services/api';\nimport { Farm, DashboardOverview, DashboardFinancial, DashboardPerformance } from '@/types';\n\nexport default function DashboardPage() {\n  const [farms, setFarms] = useState<Farm[]>([]);\n  const [selectedFarmId, setSelectedFarmId] = useState<string>('');\n  const [overview, setOverview] = useState<DashboardOverview | null>(null);\n  const [financial, setFinancial] = useState<DashboardFinancial | null>(null);\n  const [performance, setPerformance] = useState<DashboardPerformance | null>(null);\n  const [reminders, setReminders] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string>('');\n\n  useEffect(() => {\n    loadFarms();\n  }, []);\n\n  useEffect(() => {\n    if (selectedFarmId) {\n      loadDashboardData();\n    }\n  }, [selectedFarmId]);\n\n  const loadFarms = async () => {\n    try {\n      const farmsData = await farmApi.getFarms();\n      setFarms(farmsData);\n      if (farmsData.length > 0) {\n        setSelectedFarmId(farmsData[0].id);\n      }\n    } catch (err) {\n      setError('Çiftlikler yüklenirken hata oluştu');\n      console.error(err);\n    }\n  };\n\n  const loadDashboardData = async () => {\n    if (!selectedFarmId) return;\n\n    setLoading(true);\n    try {\n      const [overviewData, financialData, performanceData, remindersData] = await Promise.all([\n        dashboardApi.getOverview(selectedFarmId),\n        dashboardApi.getFinancial(selectedFarmId),\n        dashboardApi.getPerformance(selectedFarmId),\n        dashboardApi.getReminders(selectedFarmId)\n      ]);\n\n      setOverview(overviewData);\n      setFinancial(financialData);\n      setPerformance(performanceData);\n      setReminders(remindersData);\n    } catch (err) {\n      setError('Dashboard verileri yüklenirken hata oluştu');\n      console.error(err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('tr-TR', {\n      style: 'currency',\n      currency: 'TRY'\n    }).format(amount);\n  };\n\n  const formatNumber = (num: number, decimals: number = 0) => {\n    return new Intl.NumberFormat('tr-TR', {\n      minimumFractionDigits: decimals,\n      maximumFractionDigits: decimals\n    }).format(num);\n  };\n\n  const getFeedTypeLabel = (type: string) => {\n    const labels: { [key: string]: string } = {\n      'concentrate': 'Konsantre',\n      'hay': 'Kuru Ot',\n      'silage': 'Silaj',\n      'pasture': 'Mera',\n      'supplement': 'Katkı'\n    };\n    return labels[type] || type;\n  };\n\n  const getFeedTypeColor = (type: string) => {\n    const colors: { [key: string]: string } = {\n      'concentrate': 'bg-orange-100 text-orange-800',\n      'hay': 'bg-yellow-100 text-yellow-800',\n      'silage': 'bg-green-100 text-green-800',\n      'pasture': 'bg-blue-100 text-blue-800',\n      'supplement': 'bg-purple-100 text-purple-800'\n    };\n    return colors[type] || 'bg-gray-100 text-gray-800';\n  };\n\n  if (loading && !selectedFarmId) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Dashboard yükleniyor...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Dashboard</h1>\n          <p className=\"text-gray-600 mt-2\">\n            Çiftlik performansı ve finansal analiz\n          </p>\n        </div>\n      </div>\n\n      {/* Farm Selection */}\n      {farms.length > 0 && (\n        <div className=\"content-overlay p-4\">\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Çiftlik Seçin\n          </label>\n          <select\n            value={selectedFarmId}\n            onChange={(e) => setSelectedFarmId(e.target.value)}\n            className=\"w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n          >\n            {farms.map((farm) => (\n              <option key={farm.id} value={farm.id}>\n                {farm.name} - {farm.location}\n              </option>\n            ))}\n          </select>\n        </div>\n      )}\n\n      {/* Error Message */}\n      {error && (\n        <div className=\"bg-red-50 border border-red-200 rounded-md p-4\">\n          <div className=\"flex\">\n            <AlertCircle className=\"h-5 w-5 text-red-400\" />\n            <div className=\"ml-3\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Dashboard Content */}\n      {selectedFarmId && (\n        <>\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4\"></div>\n              <p className=\"text-gray-600\">Dashboard verileri yükleniyor...</p>\n            </div>\n          ) : (\n            <div className=\"space-y-6\">\n              {/* Overview Cards */}\n              {overview && (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                  {/* Toplam Hayvan */}\n                  <div className=\"content-overlay p-6 hover-glow\">\n                    <div className=\"flex items-center\">\n                      <div className=\"p-2 bg-blue-100 rounded-lg\">\n                        <Users className=\"h-6 w-6 text-blue-600\" />\n                      </div>\n                      <div className=\"ml-4\">\n                        <p className=\"text-sm font-medium text-gray-600\">Toplam Hayvan</p>\n                        <p className=\"text-2xl font-bold text-gray-900\">\n                          {overview.animal_stats.total_animals}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          {overview.animal_stats.male_count} Erkek, {overview.animal_stats.female_count} Dişi\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Aktif Rasyonlar */}\n                  <div className=\"content-overlay p-6 hover-glow\">\n                    <div className=\"flex items-center\">\n                      <div className=\"p-2 bg-green-100 rounded-lg\">\n                        <Target className=\"h-6 w-6 text-green-600\" />\n                      </div>\n                      <div className=\"ml-4\">\n                        <p className=\"text-sm font-medium text-gray-600\">Aktif Rasyonlar</p>\n                        <p className=\"text-2xl font-bold text-gray-900\">\n                          {overview.ration_stats.active_rations}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          {overview.ration_stats.total_rations} toplam rasyon\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Toplam Simülasyon */}\n                  <div className=\"content-overlay p-6 hover-glow\">\n                    <div className=\"flex items-center\">\n                      <div className=\"p-2 bg-purple-100 rounded-lg\">\n                        <BarChart3 className=\"h-6 w-6 text-purple-600\" />\n                      </div>\n                      <div className=\"ml-4\">\n                        <p className=\"text-sm font-medium text-gray-600\">Simülasyonlar</p>\n                        <p className=\"text-2xl font-bold text-gray-900\">\n                          {overview.simulation_stats.total_simulations}\n                        </p>\n                        <p className=\"text-xs text-gray-500\">\n                          Ortalama {formatCurrency(overview.simulation_stats.avg_simulation_cost || 0)}\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Hatırlatmalar */}\n                  {reminders && (\n                    <div className=\"content-overlay p-6 hover-glow\">\n                      <div className=\"flex items-center\">\n                        <div className=\"p-2 bg-red-100 rounded-lg\">\n                          <AlertCircle className=\"h-6 w-6 text-red-600\" />\n                        </div>\n                        <div className=\"ml-4\">\n                          <p className=\"text-sm font-medium text-gray-600\">Hatırlatmalar</p>\n                          <p className=\"text-2xl font-bold text-gray-900\">\n                            {reminders.today_count + reminders.overdue_count}\n                          </p>\n                          <p className=\"text-xs text-gray-500\">\n                            {reminders.overdue_count > 0 ? `${reminders.overdue_count} geciken` : 'Güncel'}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              )}\n\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                {/* Sol Kolon - Finansal Veriler */}\n                <div className=\"space-y-6\">\n                  {/* Günlük Maliyetler */}\n                  {financial && (\n                    <div className=\"content-overlay p-6\">\n                      <div className=\"flex items-center space-x-2 mb-4\">\n                        <DollarSign className=\"h-5 w-5 text-green-600\" />\n                        <h2 className=\"text-lg font-semibold text-gray-900\">Maliyet Analizi</h2>\n                      </div>\n\n                      <div className=\"space-y-4\">\n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div className=\"bg-green-50 p-3 rounded-lg\">\n                            <p className=\"text-sm text-green-600\">Günlük Maliyet</p>\n                            <p className=\"font-bold text-green-900 text-lg\">\n                              {formatCurrency(financial.daily_costs.total_daily_cost)}\n                            </p>\n                          </div>\n                          <div className=\"bg-blue-50 p-3 rounded-lg\">\n                            <p className=\"text-sm text-blue-600\">Aylık Maliyet</p>\n                            <p className=\"font-bold text-blue-900 text-lg\">\n                              {formatCurrency(financial.daily_costs.monthly_cost)}\n                            </p>\n                          </div>\n                          <div className=\"bg-purple-50 p-3 rounded-lg\">\n                            <p className=\"text-sm text-purple-600\">Yıllık Maliyet</p>\n                            <p className=\"font-bold text-purple-900 text-lg\">\n                              {formatCurrency(financial.daily_costs.yearly_cost)}\n                            </p>\n                          </div>\n                          <div className=\"bg-orange-50 p-3 rounded-lg\">\n                            <p className=\"text-sm text-orange-600\">Günlük KM</p>\n                            <p className=\"font-bold text-orange-900 text-lg\">\n                              {formatNumber(financial.daily_costs.total_daily_dm_kg, 1)} kg\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Yem Türü Dağılımı */}\n                  {financial && financial.cost_by_feed_type.length > 0 && (\n                    <div className=\"content-overlay p-6\">\n                      <div className=\"flex items-center space-x-2 mb-4\">\n                        <PieChart className=\"h-5 w-5 text-blue-600\" />\n                        <h2 className=\"text-lg font-semibold text-gray-900\">Yem Türü Dağılımı</h2>\n                      </div>\n\n                      <div className=\"space-y-3\">\n                        {financial.cost_by_feed_type.map((item, index) => (\n                          <div key={index} className=\"flex justify-between items-center\">\n                            <div className=\"flex items-center space-x-2\">\n                              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getFeedTypeColor(item.feed_type)}`}>\n                                {getFeedTypeLabel(item.feed_type)}\n                              </span>\n                              <span className=\"text-sm text-gray-600\">({item.feed_count} adet)</span>\n                            </div>\n                            <span className=\"font-medium text-gray-900\">\n                              {formatCurrency(item.avg_cost)}/kg\n                            </span>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n\n                {/* Sağ Kolon - Performans Verileri */}\n                <div className=\"space-y-6\">\n                  {/* Irk Performansı */}\n                  {performance && performance.breed_performance.length > 0 && (\n                    <div className=\"content-overlay p-6\">\n                      <div className=\"flex items-center space-x-2 mb-4\">\n                        <TrendingUp className=\"h-5 w-5 text-purple-600\" />\n                        <h2 className=\"text-lg font-semibold text-gray-900\">Irk Performansı</h2>\n                      </div>\n\n                      <div className=\"space-y-3\">\n                        {performance.breed_performance.map((breed, index) => (\n                          <div key={index} className=\"border border-gray-200 rounded-lg p-3\">\n                            <div className=\"flex justify-between items-center mb-2\">\n                              <span className=\"font-medium text-gray-900\">{breed.breed}</span>\n                              <span className=\"text-sm text-gray-600\">{breed.animal_count} hayvan</span>\n                            </div>\n\n                            <div className=\"grid grid-cols-2 gap-2 text-xs\">\n                              <div>\n                                <span className=\"text-gray-600\">Ort. Ağırlık:</span>\n                                <span className=\"font-medium ml-1\">{formatNumber(breed.avg_weight, 0)} kg</span>\n                              </div>\n                              <div>\n                                <span className=\"text-gray-600\">Ort. VKS:</span>\n                                <span className=\"font-medium ml-1\">{formatNumber(breed.avg_bcs, 1)}</span>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* En İyi Performanslar */}\n                  {performance && performance.best_performers.length > 0 && (\n                    <div className=\"content-overlay p-6\">\n                      <div className=\"flex items-center space-x-2 mb-4\">\n                        <ArrowUp className=\"h-5 w-5 text-green-600\" />\n                        <h2 className=\"text-lg font-semibold text-gray-900\">En İyi Performanslar</h2>\n                      </div>\n\n                      <div className=\"space-y-3\">\n                        {performance.best_performers.slice(0, 3).map((performer, index) => (\n                          <div key={index} className=\"bg-green-50 border border-green-200 rounded-lg p-3\">\n                            <div className=\"flex justify-between items-center mb-2\">\n                              <span className=\"font-medium text-green-900\">{performer.name}</span>\n                              <span className=\"text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full\">\n                                #{index + 1}\n                              </span>\n                            </div>\n\n                            <div className=\"grid grid-cols-3 gap-2 text-xs\">\n                              <div>\n                                <span className=\"text-green-600\">FCR:</span>\n                                <span className=\"font-medium ml-1\">{formatNumber(performer.feed_conversion_ratio, 1)}</span>\n                              </div>\n                              <div>\n                                <span className=\"text-green-600\">Maliyet:</span>\n                                <span className=\"font-medium ml-1\">{formatCurrency(performer.cost_per_kg_gain)}/kg</span>\n                              </div>\n                              <div>\n                                <span className=\"text-green-600\">Günlük:</span>\n                                <span className=\"font-medium ml-1\">{formatNumber(performer.average_daily_gain_kg, 3)} kg</span>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Yaklaşan Hatırlatmalar */}\n                  {reminders && reminders.next_reminders.length > 0 && (\n                    <div className=\"content-overlay p-6\">\n                      <div className=\"flex items-center space-x-2 mb-4\">\n                        <Calendar className=\"h-5 w-5 text-orange-600\" />\n                        <h2 className=\"text-lg font-semibold text-gray-900\">Yaklaşan Hatırlatmalar</h2>\n                      </div>\n\n                      <div className=\"space-y-3\">\n                        {reminders.next_reminders.map((reminder: any, index: number) => (\n                          <div key={index} className=\"border border-gray-200 rounded-lg p-3\">\n                            <div className=\"flex justify-between items-center mb-2\">\n                              <span className=\"font-medium text-gray-900\">{reminder.title}</span>\n                              <span className={`text-xs px-2 py-1 rounded-full ${\n                                reminder.priority === 'high' ? 'bg-red-100 text-red-800' :\n                                reminder.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :\n                                'bg-gray-100 text-gray-800'\n                              }`}>\n                                {reminder.priority === 'high' ? 'Yüksek' :\n                                 reminder.priority === 'medium' ? 'Orta' : 'Düşük'}\n                              </span>\n                            </div>\n\n                            <div className=\"text-sm text-gray-600 mb-1\">\n                              {reminder.tag} - {reminder.breed}\n                            </div>\n\n                            <div className=\"text-xs text-gray-500\">\n                              Tarih: {new Date(reminder.due_date).toLocaleDateString('tr-TR')}\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;;;AAhBA;;;;AAmBe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA4B;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6B;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+B;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,gBAAgB;gBAClB;YACF;QACF;kCAAG;QAAC;KAAe;IAEnB,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,YAAY,MAAM,yHAAA,CAAA,UAAO,CAAC,QAAQ;YACxC,SAAS;YACT,IAAI,UAAU,MAAM,GAAG,GAAG;gBACxB,kBAAkB,SAAS,CAAC,EAAE,CAAC,EAAE;YACnC;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,gBAAgB;QAErB,WAAW;QACX,IAAI;YACF,MAAM,CAAC,cAAc,eAAe,iBAAiB,cAAc,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtF,yHAAA,CAAA,eAAY,CAAC,WAAW,CAAC;gBACzB,yHAAA,CAAA,eAAY,CAAC,YAAY,CAAC;gBAC1B,yHAAA,CAAA,eAAY,CAAC,cAAc,CAAC;gBAC5B,yHAAA,CAAA,eAAY,CAAC,YAAY,CAAC;aAC3B;YAED,YAAY;YACZ,aAAa;YACb,eAAe;YACf,aAAa;QACf,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,eAAe,CAAC,KAAa,WAAmB,CAAC;QACrD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAoC;YACxC,eAAe;YACf,OAAO;YACP,UAAU;YACV,WAAW;YACX,cAAc;QAChB;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAoC;YACxC,eAAe;YACf,OAAO;YACP,UAAU;YACV,WAAW;YACX,cAAc;QAChB;QACA,OAAO,MAAM,CAAC,KAAK,IAAI;IACzB;IAEA,IAAI,WAAW,CAAC,gBAAgB;QAC9B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;YAOrC,MAAM,MAAM,GAAG,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,6LAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gCAAqB,OAAO,KAAK,EAAE;;oCACjC,KAAK,IAAI;oCAAC;oCAAI,KAAK,QAAQ;;+BADjB,KAAK,EAAE;;;;;;;;;;;;;;;;YAS3B,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;YAO5C,gCACC;0BACG,wBACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B,6LAAC;oBAAI,WAAU;;wBAEZ,0BACC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEACV,SAAS,YAAY,CAAC,aAAa;;;;;;kEAEtC,6LAAC;wDAAE,WAAU;;4DACV,SAAS,YAAY,CAAC,UAAU;4DAAC;4DAAS,SAAS,YAAY,CAAC,YAAY;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAOtF,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEACV,SAAS,YAAY,CAAC,cAAc;;;;;;kEAEvC,6LAAC;wDAAE,WAAU;;4DACV,SAAS,YAAY,CAAC,aAAa;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAO7C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;0DAEvB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEACV,SAAS,gBAAgB,CAAC,iBAAiB;;;;;;kEAE9C,6LAAC;wDAAE,WAAU;;4DAAwB;4DACzB,eAAe,SAAS,gBAAgB,CAAC,mBAAmB,IAAI;;;;;;;;;;;;;;;;;;;;;;;;gCAOjF,2BACC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,6LAAC;wDAAE,WAAU;kEACV,UAAU,WAAW,GAAG,UAAU,aAAa;;;;;;kEAElD,6LAAC;wDAAE,WAAU;kEACV,UAAU,aAAa,GAAG,IAAI,GAAG,UAAU,aAAa,CAAC,QAAQ,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASpF,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;wCAEZ,2BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAyB;;;;;;kFACtC,6LAAC;wEAAE,WAAU;kFACV,eAAe,UAAU,WAAW,CAAC,gBAAgB;;;;;;;;;;;;0EAG1D,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAAwB;;;;;;kFACrC,6LAAC;wEAAE,WAAU;kFACV,eAAe,UAAU,WAAW,CAAC,YAAY;;;;;;;;;;;;0EAGtD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAA0B;;;;;;kFACvC,6LAAC;wEAAE,WAAU;kFACV,eAAe,UAAU,WAAW,CAAC,WAAW;;;;;;;;;;;;0EAGrD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAE,WAAU;kFAA0B;;;;;;kFACvC,6LAAC;wEAAE,WAAU;;4EACV,aAAa,UAAU,WAAW,CAAC,iBAAiB,EAAE;4EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCASrE,aAAa,UAAU,iBAAiB,CAAC,MAAM,GAAG,mBACjD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACZ,UAAU,iBAAiB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtC,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAW,CAAC,wDAAwD,EAAE,iBAAiB,KAAK,SAAS,GAAG;sFAC3G,iBAAiB,KAAK,SAAS;;;;;;sFAElC,6LAAC;4EAAK,WAAU;;gFAAwB;gFAAE,KAAK,UAAU;gFAAC;;;;;;;;;;;;;8EAE5D,6LAAC;oEAAK,WAAU;;wEACb,eAAe,KAAK,QAAQ;wEAAE;;;;;;;;2DARzB;;;;;;;;;;;;;;;;;;;;;;8CAkBpB,6LAAC;oCAAI,WAAU;;wCAEZ,eAAe,YAAY,iBAAiB,CAAC,MAAM,GAAG,mBACrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACZ,YAAY,iBAAiB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzC,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAA6B,MAAM,KAAK;;;;;;sFACxD,6LAAC;4EAAK,WAAU;;gFAAyB,MAAM,YAAY;gFAAC;;;;;;;;;;;;;8EAG9D,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAK,WAAU;;wFAAoB,aAAa,MAAM,UAAU,EAAE;wFAAG;;;;;;;;;;;;;sFAExE,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAAgB;;;;;;8FAChC,6LAAC;oFAAK,WAAU;8FAAoB,aAAa,MAAM,OAAO,EAAE;;;;;;;;;;;;;;;;;;;2DAb5D;;;;;;;;;;;;;;;;wCAuBjB,eAAe,YAAY,eAAe,CAAC,MAAM,GAAG,mBACnD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACZ,YAAY,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,WAAW,sBACvD,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAA8B,UAAU,IAAI;;;;;;sFAC5D,6LAAC;4EAAK,WAAU;;gFAA6D;gFACzE,QAAQ;;;;;;;;;;;;;8EAId,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAAiB;;;;;;8FACjC,6LAAC;oFAAK,WAAU;8FAAoB,aAAa,UAAU,qBAAqB,EAAE;;;;;;;;;;;;sFAEpF,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAAiB;;;;;;8FACjC,6LAAC;oFAAK,WAAU;;wFAAoB,eAAe,UAAU,gBAAgB;wFAAE;;;;;;;;;;;;;sFAEjF,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAAiB;;;;;;8FACjC,6LAAC;oFAAK,WAAU;;wFAAoB,aAAa,UAAU,qBAAqB,EAAE;wFAAG;;;;;;;;;;;;;;;;;;;;2DAnBjF;;;;;;;;;;;;;;;;wCA6BjB,aAAa,UAAU,cAAc,CAAC,MAAM,GAAG,mBAC9C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAG,WAAU;sEAAsC;;;;;;;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;8DACZ,UAAU,cAAc,CAAC,GAAG,CAAC,CAAC,UAAe,sBAC5C,6LAAC;4DAAgB,WAAU;;8EACzB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAA6B,SAAS,KAAK;;;;;;sFAC3D,6LAAC;4EAAK,WAAW,CAAC,+BAA+B,EAC/C,SAAS,QAAQ,KAAK,SAAS,4BAC/B,SAAS,QAAQ,KAAK,WAAW,kCACjC,6BACA;sFACC,SAAS,QAAQ,KAAK,SAAS,WAC/B,SAAS,QAAQ,KAAK,WAAW,SAAS;;;;;;;;;;;;8EAI/C,6LAAC;oEAAI,WAAU;;wEACZ,SAAS,GAAG;wEAAC;wEAAI,SAAS,KAAK;;;;;;;8EAGlC,6LAAC;oEAAI,WAAU;;wEAAwB;wEAC7B,IAAI,KAAK,SAAS,QAAQ,EAAE,kBAAkB,CAAC;;;;;;;;2DAlBjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCpC;GArawB;KAAA", "debugId": null}}]}