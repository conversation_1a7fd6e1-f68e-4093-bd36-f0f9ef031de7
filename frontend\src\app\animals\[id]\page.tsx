'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { 
  ArrowLeft, 
  Edit, 
  Heart, 
  Activity, 
  Calendar,
  AlertCircle,
  CheckCircle,
  Users,
  MapPin,
  Weight,
  Ruler
} from 'lucide-react';
import { animalApi, healthApi, breedingApi } from '@/services/api';
import { Animal, VaccineSchedule, HealthRecord, BreedingRecord } from '@/types';

export default function AnimalDetailPage() {
  const router = useRouter();
  const params = useParams();
  const animalId = params.id as string;
  
  const [animal, setAnimal] = useState<Animal | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [vaccineSchedule, setVaccineSchedule] = useState<any>(null);
  const [healthRecords, setHealthRecords] = useState<HealthRecord[]>([]);
  const [breedingRecords, setBreedingRecords] = useState<BreedingRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (animalId) {
      loadAnimalData();
    }
  }, [animalId]);

  const loadAnimalData = async () => {
    setLoading(true);
    try {
      const animalData = await animalApi.getAnimal(animalId);
      setAnimal(animalData);
      
      // Paralel olarak diğer verileri yükle
      const [vaccineData, healthData, breedingData] = await Promise.all([
        healthApi.getVaccineSchedule(animalId).catch(() => null),
        healthApi.getHealthRecords(animalId).catch(() => []),
        breedingApi.getBreedingRecords(animalId).catch(() => [])
      ]);
      
      setVaccineSchedule(vaccineData);
      setHealthRecords(healthData);
      setBreedingRecords(breedingData);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Hayvan bilgileri yüklenirken hata oluştu');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const calculateAge = (birthDate: string): string => {
    const birth = new Date(birthDate);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - birth.getTime());
    const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30.44));
    
    const years = Math.floor(diffMonths / 12);
    const months = diffMonths % 12;
    
    if (years > 0) {
      return `${years} yıl ${months > 0 ? `${months} ay` : ''}`;
    }
    return `${months} ay`;
  };

  const getVaccineStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'due': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getVaccineStatusLabel = (status: string) => {
    switch (status) {
      case 'completed': return 'Tamamlandı';
      case 'due': return 'Zamanı Geldi';
      case 'overdue': return 'Gecikmiş';
      default: return 'Bekliyor';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Hayvan bilgileri yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error || !animal) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Hayvan Bulunamadı</h1>
        </div>
        
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {animal.tag} - {animal.breed}
            </h1>
            <p className="text-gray-600 mt-1">
              {animal.gender === 'male' ? 'Erkek' : 'Dişi'} • {calculateAge(animal.birth_date)}
            </p>
          </div>
        </div>
        
        <button
          onClick={() => router.push(`/animals/${animalId}/edit`)}
          className="btn-primary text-white px-4 py-2 rounded-lg flex items-center space-x-2"
        >
          <Edit className="h-4 w-4" />
          <span>Düzenle</span>
        </button>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Genel Bakış', icon: Users },
            { id: 'health', label: 'Sağlık', icon: Heart },
            { id: 'breeding', label: 'Üreme', icon: Activity },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {/* Genel Bakış */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Temel Bilgiler */}
            <div className="content-overlay p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Temel Bilgiler</h2>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-600">Küpe No</label>
                    <p className="font-medium text-gray-900">{animal.tag}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-600">Irk</label>
                    <p className="font-medium text-gray-900">{animal.breed}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-600">Cinsiyet</label>
                    <p className="font-medium text-gray-900">
                      {animal.gender === 'male' ? 'Erkek' : 'Dişi'}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-600">Yaş</label>
                    <p className="font-medium text-gray-900">{calculateAge(animal.birth_date)}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm text-gray-600">Doğum Tarihi</label>
                    <p className="font-medium text-gray-900">{formatDate(animal.birth_date)}</p>
                  </div>
                  <div>
                    <label className="text-sm text-gray-600">Kayıt Tarihi</label>
                    <p className="font-medium text-gray-900">{formatDate(animal.created_at)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Fiziksel Özellikler */}
            <div className="content-overlay p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Fiziksel Özellikler</h2>
              
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Weight className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <label className="text-sm text-gray-600">Mevcut Ağırlık</label>
                      <p className="font-medium text-gray-900">{animal.current_weight_kg} kg</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Ruler className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <label className="text-sm text-gray-600">Vücut Kondisyon Skoru</label>
                      <p className="font-medium text-gray-900">{animal.body_condition_score}/5</p>
                    </div>
                  </div>
                </div>
                
                {animal.notes && (
                  <div>
                    <label className="text-sm text-gray-600">Notlar</label>
                    <p className="font-medium text-gray-900 mt-1">{animal.notes}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Sağlık Sekmesi */}
        {activeTab === 'health' && (
          <div className="space-y-6">
            {/* Aşı Takvimi */}
            {vaccineSchedule && (
              <div className="content-overlay p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Aşı Takvimi</h2>
                
                <div className="space-y-3">
                  {vaccineSchedule.vaccine_schedule?.map((vaccine: any, index: number) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-900">{vaccine.vaccine_name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getVaccineStatusColor(vaccine.status)}`}>
                          {getVaccineStatusLabel(vaccine.status)}
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">{vaccine.description}</p>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Yaş Aralığı:</span>
                          <span className="font-medium ml-2">
                            {vaccine.age_months_min}-{vaccine.age_months_max || '∞'} ay
                          </span>
                        </div>
                        {vaccine.due_date && (
                          <div>
                            <span className="text-gray-600">Tarih:</span>
                            <span className="font-medium ml-2">{formatDate(vaccine.due_date)}</span>
                          </div>
                        )}
                      </div>
                      
                      {vaccine.days_overdue > 0 && (
                        <div className="mt-2 text-sm text-red-600">
                          {vaccine.days_overdue} gün gecikmiş
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Sağlık Kayıtları */}
            <div className="content-overlay p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Sağlık Kayıtları</h2>
              
              {healthRecords.length === 0 ? (
                <p className="text-gray-600 text-center py-4">Henüz sağlık kaydı bulunmuyor</p>
              ) : (
                <div className="space-y-3">
                  {healthRecords.map((record) => (
                    <div key={record.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium text-gray-900">{record.diagnosis}</h3>
                        <span className="text-sm text-gray-600">{formatDate(record.start_date)}</span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">{record.treatment}</p>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Veteriner:</span>
                          <span className="font-medium ml-2">{record.veterinarian}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Durum:</span>
                          <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                            record.recovery_status === 'recovered' ? 'bg-green-100 text-green-800' :
                            record.recovery_status === 'ongoing' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {record.recovery_status === 'recovered' ? 'İyileşti' :
                             record.recovery_status === 'ongoing' ? 'Devam Ediyor' : 'Kronik'}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Üreme Sekmesi */}
        {activeTab === 'breeding' && animal.gender === 'female' && (
          <div className="space-y-6">
            <div className="content-overlay p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Üreme Kayıtları</h2>
              
              {breedingRecords.length === 0 ? (
                <p className="text-gray-600 text-center py-4">Henüz üreme kaydı bulunmuyor</p>
              ) : (
                <div className="space-y-4">
                  {breedingRecords.map((record) => (
                    <div key={record.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-center mb-3">
                        <h3 className="font-medium text-gray-900">
                          Çiftleşme - {formatDate(record.breeding_date)}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          record.pregnancy_confirmed === true ? 'bg-green-100 text-green-800' :
                          record.pregnancy_confirmed === false ? 'bg-red-100 text-red-800' :
                          'bg-yellow-100 text-yellow-800'
                        }`}>
                          {record.pregnancy_confirmed === true ? 'Gebe' :
                           record.pregnancy_confirmed === false ? 'Gebe Değil' : 'Kontrol Bekliyor'}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                        <div>
                          <span className="text-gray-600">Tip:</span>
                          <span className="font-medium ml-2">
                            {record.breeding_type === 'natural' ? 'Doğal' : 'Suni Tohumlama'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Beklenen Doğum:</span>
                          <span className="font-medium ml-2">{formatDate(record.expected_calving_date)}</span>
                        </div>
                      </div>
                      
                      {record.calving_date && (
                        <div className="bg-green-50 p-3 rounded-lg">
                          <div className="flex items-center space-x-2 mb-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="font-medium text-green-900">Doğum Gerçekleşti</span>
                          </div>
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <span className="text-green-600">Doğum Tarihi:</span>
                              <span className="font-medium ml-2">{formatDate(record.calving_date)}</span>
                            </div>
                            {record.birth_weight && (
                              <div>
                                <span className="text-green-600">Doğum Ağırlığı:</span>
                                <span className="font-medium ml-2">{record.birth_weight} kg</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Erkek hayvanlar için üreme sekmesi */}
        {activeTab === 'breeding' && animal.gender === 'male' && (
          <div className="content-overlay p-6">
            <div className="text-center py-8">
              <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Erkek Hayvan Üreme Bilgileri
              </h3>
              <p className="text-gray-600">
                Erkek hayvanlar için üreme kayıtları dişi hayvanların kayıtlarında tutulmaktadır.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
