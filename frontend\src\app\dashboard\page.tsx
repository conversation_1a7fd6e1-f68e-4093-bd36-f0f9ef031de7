'use client';

import { useState, useEffect } from 'react';
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Users,
  Target,
  AlertCircle,
  Activity,
  Calendar,
  PieChart,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { dashboardApi, farmApi } from '@/services/api';
import { Farm, DashboardOverview, DashboardFinancial, DashboardPerformance } from '@/types';

export default function DashboardPage() {
  const [farms, setFarms] = useState<Farm[]>([]);
  const [selectedFarmId, setSelectedFarmId] = useState<string>('');
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [financial, setFinancial] = useState<DashboardFinancial | null>(null);
  const [performance, setPerformance] = useState<DashboardPerformance | null>(null);
  const [reminders, setReminders] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    loadFarms();
  }, []);

  useEffect(() => {
    if (selectedFarmId) {
      loadDashboardData();
    }
  }, [selectedFarmId]);

  const loadFarms = async () => {
    try {
      const farmsData = await farmApi.getFarms();
      setFarms(farmsData);
      if (farmsData.length > 0) {
        setSelectedFarmId(farmsData[0].id);
      }
    } catch (err) {
      setError('Çiftlikler yüklenirken hata oluştu');
      console.error(err);
    }
  };

  const loadDashboardData = async () => {
    if (!selectedFarmId) return;

    setLoading(true);
    try {
      const [overviewData, financialData, performanceData, remindersData] = await Promise.all([
        dashboardApi.getOverview(selectedFarmId),
        dashboardApi.getFinancial(selectedFarmId),
        dashboardApi.getPerformance(selectedFarmId),
        dashboardApi.getReminders(selectedFarmId)
      ]);

      setOverview(overviewData);
      setFinancial(financialData);
      setPerformance(performanceData);
      setReminders(remindersData);
    } catch (err) {
      setError('Dashboard verileri yüklenirken hata oluştu');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  };

  const formatNumber = (num: number, decimals: number = 0) => {
    return new Intl.NumberFormat('tr-TR', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(num);
  };

  const getFeedTypeLabel = (type: string) => {
    const labels: { [key: string]: string } = {
      'concentrate': 'Konsantre',
      'hay': 'Kuru Ot',
      'silage': 'Silaj',
      'pasture': 'Mera',
      'supplement': 'Katkı'
    };
    return labels[type] || type;
  };

  const getFeedTypeColor = (type: string) => {
    const colors: { [key: string]: string } = {
      'concentrate': 'bg-orange-100 text-orange-800',
      'hay': 'bg-yellow-100 text-yellow-800',
      'silage': 'bg-green-100 text-green-800',
      'pasture': 'bg-blue-100 text-blue-800',
      'supplement': 'bg-purple-100 text-purple-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  if (loading && !selectedFarmId) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Dashboard yükleniyor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Çiftlik performansı ve finansal analiz
          </p>
        </div>
      </div>

      {/* Farm Selection */}
      {farms.length > 0 && (
        <div className="content-overlay p-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Çiftlik Seçin
          </label>
          <select
            value={selectedFarmId}
            onChange={(e) => setSelectedFarmId(e.target.value)}
            className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            {farms.map((farm) => (
              <option key={farm.id} value={farm.id}>
                {farm.name} - {farm.location}
              </option>
            ))}
          </select>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Dashboard Content */}
      {selectedFarmId && (
        <>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Dashboard verileri yükleniyor...</p>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Overview Cards */}
              {overview && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {/* Toplam Hayvan */}
                  <div className="content-overlay p-6 hover-glow">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Users className="h-6 w-6 text-blue-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Toplam Hayvan</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {overview.animal_stats.total_animals}
                        </p>
                        <p className="text-xs text-gray-500">
                          {overview.animal_stats.male_count} Erkek, {overview.animal_stats.female_count} Dişi
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Aktif Rasyonlar */}
                  <div className="content-overlay p-6 hover-glow">
                    <div className="flex items-center">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <Target className="h-6 w-6 text-green-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Aktif Rasyonlar</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {overview.ration_stats.active_rations}
                        </p>
                        <p className="text-xs text-gray-500">
                          {overview.ration_stats.total_rations} toplam rasyon
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Toplam Simülasyon */}
                  <div className="content-overlay p-6 hover-glow">
                    <div className="flex items-center">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <BarChart3 className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="ml-4">
                        <p className="text-sm font-medium text-gray-600">Simülasyonlar</p>
                        <p className="text-2xl font-bold text-gray-900">
                          {overview.simulation_stats.total_simulations}
                        </p>
                        <p className="text-xs text-gray-500">
                          Ortalama {formatCurrency(overview.simulation_stats.avg_simulation_cost || 0)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Hatırlatmalar */}
                  {reminders && (
                    <div className="content-overlay p-6 hover-glow">
                      <div className="flex items-center">
                        <div className="p-2 bg-red-100 rounded-lg">
                          <AlertCircle className="h-6 w-6 text-red-600" />
                        </div>
                        <div className="ml-4">
                          <p className="text-sm font-medium text-gray-600">Hatırlatmalar</p>
                          <p className="text-2xl font-bold text-gray-900">
                            {reminders.today_count + reminders.overdue_count}
                          </p>
                          <p className="text-xs text-gray-500">
                            {reminders.overdue_count > 0 ? `${reminders.overdue_count} geciken` : 'Güncel'}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Sol Kolon - Finansal Veriler */}
                <div className="space-y-6">
                  {/* Günlük Maliyetler */}
                  {financial && (
                    <div className="content-overlay p-6">
                      <div className="flex items-center space-x-2 mb-4">
                        <DollarSign className="h-5 w-5 text-green-600" />
                        <h2 className="text-lg font-semibold text-gray-900">Maliyet Analizi</h2>
                      </div>

                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-green-50 p-3 rounded-lg">
                            <p className="text-sm text-green-600">Günlük Maliyet</p>
                            <p className="font-bold text-green-900 text-lg">
                              {formatCurrency(financial.daily_costs.total_daily_cost)}
                            </p>
                          </div>
                          <div className="bg-blue-50 p-3 rounded-lg">
                            <p className="text-sm text-blue-600">Aylık Maliyet</p>
                            <p className="font-bold text-blue-900 text-lg">
                              {formatCurrency(financial.daily_costs.monthly_cost)}
                            </p>
                          </div>
                          <div className="bg-purple-50 p-3 rounded-lg">
                            <p className="text-sm text-purple-600">Yıllık Maliyet</p>
                            <p className="font-bold text-purple-900 text-lg">
                              {formatCurrency(financial.daily_costs.yearly_cost)}
                            </p>
                          </div>
                          <div className="bg-orange-50 p-3 rounded-lg">
                            <p className="text-sm text-orange-600">Günlük KM</p>
                            <p className="font-bold text-orange-900 text-lg">
                              {formatNumber(financial.daily_costs.total_daily_dm_kg, 1)} kg
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Yem Türü Dağılımı */}
                  {financial && financial.cost_by_feed_type.length > 0 && (
                    <div className="content-overlay p-6">
                      <div className="flex items-center space-x-2 mb-4">
                        <PieChart className="h-5 w-5 text-blue-600" />
                        <h2 className="text-lg font-semibold text-gray-900">Yem Türü Dağılımı</h2>
                      </div>

                      <div className="space-y-3">
                        {financial.cost_by_feed_type.map((item, index) => (
                          <div key={index} className="flex justify-between items-center">
                            <div className="flex items-center space-x-2">
                              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getFeedTypeColor(item.feed_type)}`}>
                                {getFeedTypeLabel(item.feed_type)}
                              </span>
                              <span className="text-sm text-gray-600">({item.feed_count} adet)</span>
                            </div>
                            <span className="font-medium text-gray-900">
                              {formatCurrency(item.avg_cost)}/kg
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Sağ Kolon - Performans Verileri */}
                <div className="space-y-6">
                  {/* Irk Performansı */}
                  {performance && performance.breed_performance.length > 0 && (
                    <div className="content-overlay p-6">
                      <div className="flex items-center space-x-2 mb-4">
                        <TrendingUp className="h-5 w-5 text-purple-600" />
                        <h2 className="text-lg font-semibold text-gray-900">Irk Performansı</h2>
                      </div>

                      <div className="space-y-3">
                        {performance.breed_performance.map((breed, index) => (
                          <div key={index} className="border border-gray-200 rounded-lg p-3">
                            <div className="flex justify-between items-center mb-2">
                              <span className="font-medium text-gray-900">{breed.breed}</span>
                              <span className="text-sm text-gray-600">{breed.animal_count} hayvan</span>
                            </div>

                            <div className="grid grid-cols-2 gap-2 text-xs">
                              <div>
                                <span className="text-gray-600">Ort. Ağırlık:</span>
                                <span className="font-medium ml-1">{formatNumber(breed.avg_weight, 0)} kg</span>
                              </div>
                              <div>
                                <span className="text-gray-600">Ort. VKS:</span>
                                <span className="font-medium ml-1">{formatNumber(breed.avg_bcs, 1)}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* En İyi Performanslar */}
                  {performance && performance.best_performers.length > 0 && (
                    <div className="content-overlay p-6">
                      <div className="flex items-center space-x-2 mb-4">
                        <ArrowUp className="h-5 w-5 text-green-600" />
                        <h2 className="text-lg font-semibold text-gray-900">En İyi Performanslar</h2>
                      </div>

                      <div className="space-y-3">
                        {performance.best_performers.slice(0, 3).map((performer, index) => (
                          <div key={index} className="bg-green-50 border border-green-200 rounded-lg p-3">
                            <div className="flex justify-between items-center mb-2">
                              <span className="font-medium text-green-900">{performer.name}</span>
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                #{index + 1}
                              </span>
                            </div>

                            <div className="grid grid-cols-3 gap-2 text-xs">
                              <div>
                                <span className="text-green-600">FCR:</span>
                                <span className="font-medium ml-1">{formatNumber(performer.feed_conversion_ratio, 1)}</span>
                              </div>
                              <div>
                                <span className="text-green-600">Maliyet:</span>
                                <span className="font-medium ml-1">{formatCurrency(performer.cost_per_kg_gain)}/kg</span>
                              </div>
                              <div>
                                <span className="text-green-600">Günlük:</span>
                                <span className="font-medium ml-1">{formatNumber(performer.average_daily_gain_kg, 3)} kg</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Yaklaşan Hatırlatmalar */}
                  {reminders && reminders.next_reminders.length > 0 && (
                    <div className="content-overlay p-6">
                      <div className="flex items-center space-x-2 mb-4">
                        <Calendar className="h-5 w-5 text-orange-600" />
                        <h2 className="text-lg font-semibold text-gray-900">Yaklaşan Hatırlatmalar</h2>
                      </div>

                      <div className="space-y-3">
                        {reminders.next_reminders.map((reminder: any, index: number) => (
                          <div key={index} className="border border-gray-200 rounded-lg p-3">
                            <div className="flex justify-between items-center mb-2">
                              <span className="font-medium text-gray-900">{reminder.title}</span>
                              <span className={`text-xs px-2 py-1 rounded-full ${
                                reminder.priority === 'high' ? 'bg-red-100 text-red-800' :
                                reminder.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-gray-100 text-gray-800'
                              }`}>
                                {reminder.priority === 'high' ? 'Yüksek' :
                                 reminder.priority === 'medium' ? 'Orta' : 'Düşük'}
                              </span>
                            </div>

                            <div className="text-sm text-gray-600 mb-1">
                              {reminder.tag} - {reminder.breed}
                            </div>

                            <div className="text-xs text-gray-500">
                              Tarih: {new Date(reminder.due_date).toLocaleDateString('tr-TR')}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
