'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { 
  ArrowLeft, 
  TrendingUp, 
  AlertCircle,
  BarChart3,
  DollarSign,
  Target,
  Clock,
  Users,
  MapPin,
  Calendar
} from 'lucide-react';
import { simulationApi } from '@/services/api';

interface SimulationDetails {
  simulation: {
    id: string;
    name: string;
    description?: string;
    start_age_months: number;
    end_age_months: number;
    slaughter_age_months?: number;
    total_feed_cost: number;
    total_feed_consumption_kg: number;
    created_at: string;
    breed?: string;
    gender?: string;
    current_weight_kg?: number;
    farm_name?: string;
    farm_location?: string;
  };
  details: Array<{
    month: number;
    age_months: number;
    weight_kg: number;
    daily_gain_kg: number;
    feed_consumption_kg_per_day: number;
    monthly_feed_cost: number;
    monthly_feed_consumption_kg: number;
    physiological_stage: string;
    is_pregnant: boolean;
    is_lactating: boolean;
  }>;
  summary: {
    total_months: number;
    initial_weight_kg: number;
    final_weight_kg: number;
    total_weight_gain_kg: number;
    average_daily_gain_kg: number;
    average_daily_consumption_kg: number;
    total_feed_cost: number;
    total_feed_consumption_kg: number;
    cost_per_kg_gain: number;
    feed_conversion_ratio: number;
    stages_analysis: {
      [stage: string]: {
        months: number;
        total_cost: number;
        total_consumption: number;
      };
    };
  };
}

export default function SimulationDetailPage() {
  const router = useRouter();
  const params = useParams();
  const simulationId = params.id as string;
  
  const [details, setDetails] = useState<SimulationDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (simulationId) {
      loadSimulationDetails();
    }
  }, [simulationId]);

  const loadSimulationDetails = async () => {
    setLoading(true);
    try {
      const data = await simulationApi.getSimulationDetails(simulationId);
      setDetails(data);
    } catch (err: any) {
      setError(err.response?.data?.detail || 'Simülasyon detayları yüklenirken hata oluştu');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStageLabel = (stage: string) => {
    const labels: { [key: string]: string } = {
      'suckling': 'Emziren Buzağı',
      'weaning': 'Sütten Kesim',
      'growing': 'Büyüme',
      'yearling': 'Düve/Tosun',
      'heifer': 'Düve',
      'breeding': 'Üreme',
      'bull': 'Boğa'
    };
    return labels[stage] || stage;
  };

  const getStageColor = (stage: string) => {
    const colors: { [key: string]: string } = {
      'suckling': 'bg-pink-100 text-pink-800',
      'weaning': 'bg-yellow-100 text-yellow-800',
      'growing': 'bg-green-100 text-green-800',
      'yearling': 'bg-blue-100 text-blue-800',
      'heifer': 'bg-purple-100 text-purple-800',
      'breeding': 'bg-red-100 text-red-800',
      'bull': 'bg-gray-100 text-gray-800'
    };
    return colors[stage] || 'bg-gray-100 text-gray-800';
  };

  const getSimulationDuration = () => {
    if (!details) return '';
    const months = details.simulation.end_age_months - details.simulation.start_age_months;
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;
    
    if (years > 0) {
      return `${years} yıl ${remainingMonths > 0 ? `${remainingMonths} ay` : ''}`;
    }
    return `${months} ay`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Simülasyon detayları yükleniyor...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Simülasyon Detayları</h1>
        </div>
        
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!details) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <h1 className="text-3xl font-bold text-gray-900">Simülasyon Bulunamadı</h1>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{details.simulation.name}</h1>
            <p className="text-gray-600 mt-1">
              {details.simulation.description || 'Yaşam boyu simülasyon analizi'}
            </p>
          </div>
        </div>
      </div>

      {/* Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Çiftlik Bilgisi */}
        <div className="content-overlay p-4">
          <div className="flex items-center space-x-2 mb-2">
            <MapPin className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Çiftlik</span>
          </div>
          <p className="font-semibold text-gray-900">{details.simulation.farm_name}</p>
          <p className="text-sm text-gray-600">{details.simulation.farm_location}</p>
        </div>

        {/* Hayvan Bilgisi */}
        <div className="content-overlay p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Users className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Hayvan</span>
          </div>
          <p className="font-semibold text-gray-900">
            {details.simulation.breed} - {details.simulation.gender === 'male' ? 'Erkek' : 'Dişi'}
          </p>
          <p className="text-sm text-gray-600">
            Başlangıç: {details.summary.initial_weight_kg} kg
          </p>
        </div>

        {/* Simülasyon Süresi */}
        <div className="content-overlay p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Clock className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Süre</span>
          </div>
          <p className="font-semibold text-gray-900">{getSimulationDuration()}</p>
          <p className="text-sm text-gray-600">
            {details.simulation.start_age_months} - {details.simulation.end_age_months} ay
          </p>
        </div>

        {/* Oluşturma Tarihi */}
        <div className="content-overlay p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Oluşturulma</span>
          </div>
          <p className="font-semibold text-gray-900">
            {formatDate(details.simulation.created_at)}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sol Kolon - Özet İstatistikler */}
        <div className="space-y-6">
          {/* Toplam Sonuçlar */}
          <div className="content-overlay p-6">
            <div className="flex items-center space-x-2 mb-4">
              <BarChart3 className="h-5 w-5 text-green-600" />
              <h2 className="text-lg font-semibold text-gray-900">Toplam Sonuçlar</h2>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm text-blue-600">Toplam Maliyet</p>
                  <p className="font-bold text-blue-900 text-lg">
                    {formatCurrency(details.summary.total_feed_cost)}
                  </p>
                </div>
                <div className="bg-green-50 p-3 rounded-lg">
                  <p className="text-sm text-green-600">Toplam Yem</p>
                  <p className="font-bold text-green-900 text-lg">
                    {details.summary.total_feed_consumption_kg.toLocaleString('tr-TR')} kg
                  </p>
                </div>
                <div className="bg-purple-50 p-3 rounded-lg">
                  <p className="text-sm text-purple-600">Ağırlık Artışı</p>
                  <p className="font-bold text-purple-900 text-lg">
                    {details.summary.total_weight_gain_kg.toFixed(0)} kg
                  </p>
                </div>
                <div className="bg-orange-50 p-3 rounded-lg">
                  <p className="text-sm text-orange-600">Final Ağırlık</p>
                  <p className="font-bold text-orange-900 text-lg">
                    {details.summary.final_weight_kg.toFixed(0)} kg
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Performans Metrikleri */}
          <div className="content-overlay p-6">
            <div className="flex items-center space-x-2 mb-4">
              <Target className="h-5 w-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">Performans Metrikleri</h2>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Ortalama Günlük Artış:</span>
                <span className="font-semibold text-gray-900">
                  {details.summary.average_daily_gain_kg.toFixed(3)} kg/gün
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Ortalama Günlük Tüketim:</span>
                <span className="font-semibold text-gray-900">
                  {details.summary.average_daily_consumption_kg.toFixed(1)} kg/gün
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Yem Dönüşüm Oranı:</span>
                <span className="font-semibold text-gray-900">
                  {details.summary.feed_conversion_ratio.toFixed(1)}
                </span>
              </div>
              <div className="flex justify-between items-center border-t pt-3">
                <span className="text-gray-600 font-medium">Kg Artış Maliyeti:</span>
                <span className="font-bold text-gray-900">
                  {formatCurrency(details.summary.cost_per_kg_gain)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Sağ Kolon - Dönemsel Analiz */}
        <div className="space-y-6">
          {/* Fizyolojik Dönemler */}
          <div className="content-overlay p-6">
            <div className="flex items-center space-x-2 mb-4">
              <DollarSign className="h-5 w-5 text-purple-600" />
              <h2 className="text-lg font-semibold text-gray-900">Dönemsel Analiz</h2>
            </div>
            
            <div className="space-y-3">
              {Object.entries(details.summary.stages_analysis).map(([stage, data]) => (
                <div key={stage} className="border border-gray-200 rounded-lg p-3">
                  <div className="flex justify-between items-center mb-2">
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStageColor(stage)}`}>
                      {getStageLabel(stage)}
                    </span>
                    <span className="text-sm text-gray-600">{data.months} ay</span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="text-gray-600">Maliyet:</span>
                      <span className="font-medium ml-1">{formatCurrency(data.total_cost)}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Yem:</span>
                      <span className="font-medium ml-1">{data.total_consumption.toFixed(0)} kg</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Aylık Trend (Son 12 ay) */}
          <div className="content-overlay p-6">
            <div className="flex items-center space-x-2 mb-4">
              <TrendingUp className="h-5 w-5 text-orange-600" />
              <h2 className="text-lg font-semibold text-gray-900">Son 12 Ay Trendi</h2>
            </div>
            
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {details.details.slice(-12).map((detail, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                  <div>
                    <span className="text-sm font-medium text-gray-900">
                      {detail.age_months} ay
                    </span>
                    <span className={`ml-2 inline-block px-2 py-1 rounded-full text-xs ${getStageColor(detail.physiological_stage)}`}>
                      {getStageLabel(detail.physiological_stage)}
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      {detail.weight_kg.toFixed(0)} kg
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatCurrency(detail.monthly_feed_cost)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
