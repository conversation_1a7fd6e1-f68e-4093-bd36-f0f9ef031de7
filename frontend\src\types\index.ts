// Enum tanımları
export enum CattleBreed {
  ANGUS = "angus",
  HEREFORD = "hereford",
  SIMMENTAL = "simmental",
  CHAROLAIS = "charolais",
  LIMOUSIN = "limousin",
  HOLSTEIN = "holstein",
  BROWN_SWISS = "brown_swiss",
  NATIVE_ANATOLIAN = "native_anatolian",
  CROSSBRED = "crossbred"
}

export enum AnimalStatus {
  CALF = "calf",
  YOUNG = "young",
  BREEDING = "breeding",
  FATTENING = "fattening",
  READY_FOR_SALE = "ready_for_sale",
  SOLD = "sold",
  DEAD = "dead"
}

export enum FeedType {
  CONCENTRATE = "concentrate",
  ROUGHAGE = "roughage",
  HAY = "hay",
  SILAGE = "silage",
  PASTURE = "pasture",
  MINERAL_VITAMIN = "mineral_vitamin"
}

export enum Gender {
  MALE = "male",
  FEMALE = "female"
}

// Interface tanımları
export interface Farm {
  id: string;
  name: string;
  location: string;
  established_date: string;
  total_land_hectares: number;
  pasture_land_hectares: number;
  barn_capacity: number;
  feed_storage_capacity_tons: number;
  silage_capacity_tons: number;
  hay_storage_capacity_tons: number;
  water_storage_capacity_liters: number;
  milking_parlor_capacity?: number;
  quarantine_facility_capacity: number;
  hospital_pen_capacity: number;
  handling_facility_present: boolean;
  scale_capacity_kg: number;
}

export interface Animal {
  id: string;
  farm_id: string;
  breed: CattleBreed;
  birth_date: string;
  gender: Gender;
  current_weight_kg: number;
  body_condition_score: number;
  status: AnimalStatus;
  is_pregnant: boolean;
  age_months: number;
  pregnancy_start_date?: string;
  expected_calving_date?: string;
  dam_id?: string;
  sire_id?: string;
  purchase_price?: number;
  purchase_date?: string;
}

export interface Feed {
  id: string;
  farm_id: string;
  name: string;
  feed_type: FeedType;
  cost_per_kg: number;
  dry_matter_percentage: number;
  crude_protein_percentage: number;
  metabolizable_energy_mcal_kg: number;
  storage_life_days: number;
  moisture_content_percentage: number;
}

export interface FarmCreate {
  name: string;
  location: string;
  total_land_hectares: number;
  pasture_land_hectares: number;
  barn_capacity: number;
  feed_storage_capacity_tons: number;
  silage_capacity_tons: number;
  hay_storage_capacity_tons: number;
  water_storage_capacity_liters: number;
  milking_parlor_capacity?: number;
  quarantine_facility_capacity: number;
  hospital_pen_capacity: number;
  handling_facility_present: boolean;
  scale_capacity_kg: number;
  labor_cost_monthly?: number;
  electricity_cost_monthly?: number;
  water_cost_monthly?: number;
  fuel_cost_monthly?: number;
  insurance_cost_monthly?: number;
  maintenance_cost_monthly?: number;
  veterinary_cost_annual?: number;
  taxation_annual?: number;
  depreciation_annual?: number;
  interest_cost_annual?: number;
  vaccination_cost_per_animal?: number;
  deworming_cost_per_animal?: number;
  hoof_care_cost_per_animal?: number;
  breeding_cost_per_service?: number;
  live_cattle_price_per_kg?: number;
  calf_price_per_head?: number;
  bull_price_per_head?: number;
  cow_price_per_head?: number;
  carcass_price_per_kg?: number;
  manure_price_per_ton?: number;
  hide_price_per_piece?: number;
  conception_rate?: number;
  calving_rate?: number;
  calf_survival_rate?: number;
  weaning_rate?: number;
  mortality_rate_adult?: number;
  mortality_rate_young?: number;
  culling_rate?: number;
  replacement_rate?: number;
  disease_outbreak_probability?: number;
  market_volatility_coefficient?: number;
  weather_risk_probability?: number;
  feed_price_volatility?: number;
  drought_probability?: number;
  flood_probability?: number;
  policy_change_risk?: number;
  discount_rate?: number;
  inflation_rate?: number;
  tax_rate?: number;
}

export interface AnimalCreate {
  farm_id: string;
  breed: CattleBreed;
  birth_date: string;
  gender: Gender;
  current_weight_kg: number;
  body_condition_score: number;
  status: AnimalStatus;
  is_pregnant?: boolean;
  pregnancy_start_date?: string;
  expected_calving_date?: string;
  dam_id?: string;
  sire_id?: string;
  purchase_price?: number;
  purchase_date?: string;
}

export interface AnimalStats {
  total_animals: number;
  by_gender: {
    male: number;
    female: number;
  };
  by_status: Record<string, number>;
  by_breed: Record<string, number>;
  average_weight: number;
  pregnant_count: number;
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  message?: string;
  error?: string;
}

// Breed characteristics
export interface BreedCharacteristics {
  breed: CattleBreed;
  mature_weight_male_kg: number;
  mature_weight_female_kg: number;
  birth_weight_kg: number;
  weaning_weight_kg: number;
  yearling_weight_kg: number;
  average_daily_gain_kg: number;
  feed_conversion_ratio: number;
  dressing_percentage: number;
  age_at_first_calving_months: number;
  gestation_length_days: number;
  calving_interval_days: number;
  longevity_years: number;
  calving_ease_score: number;
  milk_production_potential_kg: number;
}

// Ration types
export type RationType = 'individual' | 'group' | 'template';
export type OptimizationObjective = 'cost' | 'performance' | 'balanced';
export type TargetGroup = 'calves' | 'heifers' | 'lactating_cows' | 'dry_cows' | 'bulls';

export interface NutritionalRequirement {
  animal_id: string;
  dry_matter_intake_kg: number;
  energy_requirements: {
    net_energy_maintenance: number;
    net_energy_gain: number;
    pregnancy_energy: number;
    lactation_energy: number;
    total_net_energy: number;
    metabolizable_energy: number;
  };
  protein_requirements: {
    metabolizable_protein_maintenance: number;
    metabolizable_protein_gain: number;
    pregnancy_protein: number;
    lactation_protein: number;
    total_metabolizable_protein: number;
    crude_protein: number;
    rumen_degradable_protein: number;
    rumen_undegradable_protein: number;
  };
  mineral_requirements: {
    calcium: number;
    phosphorus: number;
  };
}

export interface RationComponent {
  id: string;
  feed_name: string;
  feed_type: string;
  amount_kg_per_day: number;
  percentage_of_total_dm: number;
  dry_matter_contribution_kg: number;
  protein_contribution_kg: number;
  energy_contribution_mcal: number;
  calcium_contribution_kg: number;
  phosphorus_contribution_kg: number;
  cost_contribution: number;
}

export interface Ration {
  id: string;
  farm_id: string;
  animal_id?: string;
  name: string;
  description?: string;
  ration_type: RationType;
  target_group?: TargetGroup;
  total_cost_per_day: number;
  total_dry_matter_kg: number;
  total_crude_protein_percentage: number;
  total_metabolizable_energy_mcal: number;
  total_calcium_percentage: number;
  total_phosphorus_percentage: number;
  is_optimized: boolean;
  optimization_objective?: OptimizationObjective;
  optimization_score?: number;
  is_active: boolean;
  created_at: string;
}

export interface RationDetails {
  ration: Ration;
  components: RationComponent[];
}

export interface OptimizationRequest {
  farm_id: string;
  animal_id: string;
  objective: OptimizationObjective;
  name: string;
  description?: string;
}

export interface OptimizationResult {
  ration_id: string;
  message: string;
  optimization_result: {
    total_cost_per_day: number;
    total_dry_matter_kg: number;
    components: Array<{
      feed_name: string;
      amount_kg: number;
      cost: number;
    }>;
    adequacy_ratios: {
      protein_adequacy: number;
      energy_adequacy: number;
      calcium_adequacy: number;
      phosphorus_adequacy: number;
    };
  };
}

// Simulation types
export type SimulationType = 'lifetime' | 'period' | 'comparison';
export type PhysiologicalStage = 'suckling' | 'weaning' | 'growing' | 'yearling' | 'heifer' | 'breeding' | 'bull';

export interface SimulationRequest {
  animal_id: string;
  farm_id: string;
  name: string;
  end_age_months: number;
  slaughter_age_months?: number;
}

export interface SimulationDetail {
  id: string;
  simulation_id: string;
  month: number;
  age_months: number;
  weight_kg: number;
  daily_gain_kg: number;
  feed_consumption_kg_per_day: number;
  monthly_feed_cost: number;
  monthly_feed_consumption_kg: number;
  physiological_stage: PhysiologicalStage;
  is_pregnant: boolean;
  is_lactating: boolean;
}

export interface Simulation {
  id: string;
  farm_id: string;
  animal_id: string;
  name: string;
  description?: string;
  simulation_type: SimulationType;
  start_age_months: number;
  end_age_months: number;
  target_weight_kg?: number;
  slaughter_age_months?: number;
  breeding_start_months?: number;
  total_feed_cost: number;
  total_feed_consumption_kg: number;
  total_revenue?: number;
  net_profit?: number;
  roi_percentage?: number;
  break_even_months?: number;
  created_at: string;
  // Join fields
  breed?: string;
  gender?: string;
  current_weight_kg?: number;
  farm_name?: string;
  farm_location?: string;
}

export interface SimulationSummary {
  total_months: number;
  initial_weight_kg: number;
  final_weight_kg: number;
  total_weight_gain_kg: number;
  average_daily_gain_kg: number;
  average_daily_consumption_kg: number;
  total_feed_cost: number;
  total_feed_consumption_kg: number;
  cost_per_kg_gain: number;
  feed_conversion_ratio: number;
  stages_analysis: {
    [stage: string]: {
      months: number;
      total_cost: number;
      total_consumption: number;
    };
  };
}

export interface SimulationDetails {
  simulation: Simulation;
  details: SimulationDetail[];
  summary: SimulationSummary;
}

export interface SimulationResult {
  simulation_id: string;
  message: string;
  status: string;
}

// Dashboard types
export interface DashboardOverview {
  animal_stats: {
    total_animals: number;
    male_count: number;
    female_count: number;
    avg_weight: number;
    avg_age_months: number;
  };
  feed_stats: {
    total_feeds: number;
    avg_cost_per_kg: number;
    concentrate_count: number;
    hay_count: number;
    silage_count: number;
  };
  ration_stats: {
    total_rations: number;
    active_rations: number;
    optimized_rations: number;
  };
  simulation_stats: {
    total_simulations: number;
    avg_simulation_cost: number;
    avg_feed_consumption: number;
  };
  recent_activity: {
    new_animals_30d: number;
    new_rations_30d: number;
    new_simulations_30d: number;
  };
}

export interface DashboardFinancial {
  daily_costs: {
    total_daily_cost: number;
    total_daily_dm_kg: number;
    weekly_cost: number;
    monthly_cost: number;
    yearly_cost: number;
  };
  active_rations: Array<{
    id: string;
    name: string;
    daily_cost: number;
    daily_dm_kg: number;
  }>;
  cost_by_feed_type: Array<{
    feed_type: string;
    avg_cost: number;
    feed_count: number;
  }>;
  projections: {
    avg_lifetime_cost: number;
    avg_lifetime_consumption: number;
    simulation_count: number;
  };
}

export interface DashboardPerformance {
  breed_performance: Array<{
    breed: string;
    animal_count: number;
    avg_weight: number;
    avg_bcs: number;
  }>;
  recent_simulations: Array<{
    id: string;
    name: string;
    total_feed_cost: number;
    total_feed_consumption_kg: number;
    total_months: number;
    total_weight_gain_kg: number;
    average_daily_gain_kg: number;
    feed_conversion_ratio: number;
    cost_per_kg_gain: number;
  }>;
  best_performers: Array<{
    name: string;
    feed_conversion_ratio: number;
    cost_per_kg_gain: number;
    average_daily_gain_kg: number;
  }>;
}

// Health Management types
export type VaccineStatus = 'pending' | 'due' | 'overdue' | 'completed';
export type HealthRecordType = 'illness' | 'treatment' | 'checkup' | 'injury';
export type RecoveryStatus = 'ongoing' | 'recovered' | 'chronic';

export interface VaccineSchedule {
  id: string;
  breed: string;
  vaccine_name: string;
  age_months_min: number;
  age_months_max?: number;
  repeat_interval_months?: number;
  season_requirement?: string;
  is_mandatory: boolean;
  description: string;
  cost_estimate: number;
  status?: VaccineStatus;
  due_date?: string;
  days_overdue?: number;
  last_vaccination?: AnimalVaccination;
}

export interface AnimalVaccination {
  id: string;
  animal_id: string;
  vaccine_name: string;
  vaccination_date: string;
  next_due_date?: string;
  veterinarian: string;
  batch_number: string;
  cost: number;
  notes: string;
  created_at: string;
}

export interface HealthRecord {
  id: string;
  animal_id: string;
  record_type: HealthRecordType;
  diagnosis: string;
  treatment: string;
  medication: string;
  veterinarian: string;
  cost: number;
  start_date: string;
  end_date?: string;
  recovery_status: RecoveryStatus;
  notes: string;
  created_at: string;
}

// Breeding Management types
export type BreedingType = 'natural' | 'artificial';
export type CalvingEase = 1 | 2 | 3 | 4 | 5;

export interface BreedingRecord {
  id: string;
  female_id: string;
  male_id?: string;
  breeding_date: string;
  breeding_type: BreedingType;
  semen_source?: string;
  expected_calving_date: string;
  pregnancy_confirmed?: boolean;
  pregnancy_check_date?: string;
  pregnancy_check_method?: string;
  veterinarian: string;
  cost: number;
  notes: string;
  created_at: string;
  // Join fields
  male_tag?: string;
  calving_date?: string;
  birth_weight?: number;
  calving_ease?: CalvingEase;
}

export interface CalvingRecord {
  id: string;
  mother_id: string;
  calf_id?: string;
  calving_date: string;
  calving_ease: CalvingEase;
  birth_weight?: number;
  complications?: string;
  veterinarian_assisted: boolean;
  veterinarian: string;
  cost: number;
  notes: string;
  created_at: string;
}

export interface BreedingCalendar {
  upcoming_calvings: Array<BreedingRecord & { tag: string; breed: string }>;
  pregnancy_checks: Array<BreedingRecord & { tag: string; breed: string }>;
  ready_for_breeding: Array<Animal & { calving_date?: string }>;
}

// Reminder System types
export type ReminderType = 'vaccination' | 'health_checkup' | 'pregnancy_check' | 'calving_preparation' | 'postpartum_checkup' | 'custom';
export type ReminderPriority = 'low' | 'medium' | 'high';

export interface Reminder {
  id: string;
  animal_id: string;
  reminder_type: ReminderType;
  title: string;
  description: string;
  due_date: string;
  priority: ReminderPriority;
  is_completed: boolean;
  completed_date?: string;
  created_at: string;
  // Join fields
  tag?: string;
  breed?: string;
  gender?: string;
  days_overdue?: number;
  days_until?: number;
}

export interface ReminderSummary {
  overdue: Reminder[];
  today: Reminder[];
  upcoming: Reminder[];
  total_count: number;
}

export interface DashboardReminders {
  today_count: number;
  overdue_count: number;
  upcoming_count: number;
  next_reminders: Reminder[];
}
